syntax = "proto3";

package com.blsc.grpc.activity;

option java_multiple_files = true;
option java_package = "com.blsc.grpc.activity";
option java_outer_classname = "ActivityProto";

import "google/protobuf/timestamp.proto";

service ActivityService {
  // 列出活动，支持分页
  rpc ListActivities (ListActivitiesRequest) returns (ListActivitiesResponse) {}
}

message Activity {
  int64 id = 1; // 活动ID
  string title = 2; // 活动标题
  string cover_url = 3; // 活动封面URL
  google.protobuf.Timestamp activity_time = 4; // 活动时间
  string address = 5; // 活动地址
  string position = 6; // 活动地址经纬度
  int32 status = 7; // 活动状态，1-进行中 2-已结束
  int32 registration_count = 8; // 已报名人数
  repeated int64 user_ids = 9; // 已参与活动的用户ID列表
}

message ListActivitiesRequest {
  int32 status = 1; // 活动状态，1-进行中 2-已结束
  int32 page_number = 2; // 页码，从1开始
  int32 page_size = 3;   // 每页大小
}

message ListActivitiesResponse {
  int32 page_number = 1; // 当前页码
  int32 page_size = 2;   // 每页大小
  int32 total_pages = 3; // 总页数
  int32 total_count = 4; // 总记录数
  repeated Activity activities = 5; // 活动列表
}