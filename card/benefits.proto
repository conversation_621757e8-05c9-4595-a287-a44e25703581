syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.blsc.benefits.grpc";
/**
根据权益编码查询权益信息
 */
message BenefitsCodeRequest{
  int32 benefits_code=1;
}
/**
根据权益id查询权益信息
 */
message  BenefitsIdRequest{
  int64  benefits_id=1;
}

/**
权益查询返回结果
 */
message BenefitsInfoResponse{
  //年卡类别编码
  int32  card_code=1;
  //权益id
  int64 benefits_id=2;
  //权益编码
  int32 benefits_code=3;
  //权益标题
  string  benefits_title=4;
  //权益二级标题
  string  benefits_sub_title=5;
  //年卡名称
  string  card_title=6;
}
/**
grpc服务
 */
service  BenefitsService{
  /**
  根据权益id查询权益信息
   */
  rpc requestBenefitsById(BenefitsIdRequest) returns (BenefitsInfoResponse);
  /**
  根据权益编码查询权益信息
   */
  rpc requestBenefitsByCode(BenefitsCodeRequest) returns (BenefitsInfoResponse);
}
