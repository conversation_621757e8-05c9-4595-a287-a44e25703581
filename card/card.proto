syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.blsc.card.grpc";

/**
用户id参数
 */
message UsersIdRequest{
  int64 users_id = 1;
}

/**
根据年卡编码查询年卡信息
 */
message CardRequest{
  int32 card_code = 1;
}

/**
年卡信息返回值
 */
message CardResponse{
  /**
  年卡主键
   */
  int64  card_id = 1;
  /**
  年卡编号
   */
  int32  card_code = 2;
  /**
  年卡票题
   */
  string card_title = 3;
  /**
  年卡二级标题
   */
  string card_sub_title = 4;
  /**
  年卡分类编码
   */
  int32  card_type_code = 5;
  /**
  原价
   */
  int32  card_original_price = 6;
  /**
  现价
   */
  int32  card_current_price = 7;
  /**
  介绍
   */
  string card_introduce = 8;
  /**
  图片url
   */
  string card_image_url = 9;
}
/**
用户年卡message
 */
message UsersCardResponse{

  /**
    * 年卡产品编号,卡种,1000:优享卡 1001：尊享卡
    */
  int32 card_code = 1;
  /**
   * 年卡编号
   */
  string card_num = 2;
  /**
   * 年卡状态 年卡状态 0,正常状态,2:异常状态,3:其它异常状态
   */
  int32 card_state = 3;

}


//小桔子同步年卡
message SyncOrangeRequest{
  //用户id
  int64  users_id = 1;
  //同步的年卡剩余天数
  int32  day = 2;
}
//请求响应状态
message  StateResponse{
  int32  status = 1;
}

/**
年卡服务
 */
service CardService{

  /**
  根据用户ID，查询用户的年卡信息
   */
  rpc requestUsersCard(UsersIdRequest) returns (UsersCardResponse);

  /**
  根据年卡编码获取年卡信息
   */
  rpc requestCardByCode(CardRequest) returns (CardResponse);
  /**
  同步小桔子年卡 grpc服务
   */
  rpc syncOrangeCard(SyncOrangeRequest) returns(StateResponse);
}
