syntax = "proto3";

package com.blsc.distribution;

import "google/rpc/status.proto";

option java_multiple_files = true;
option java_package = "com.blsc.distribution.grpc";
import "google/protobuf/timestamp.proto";


service DistributionConfigService {
  // 获取启用的分销配置
  rpc GetEnableConfig (GetEnableConfigRequest) returns (GetEnableConfigResponse) {};
}

message GetEnableConfigRequest {
  //商品id
  int64 product_id = 1;
}

message GetEnableConfigResponse {
  //数据id
  int64 id = 1;
  //商品id
  int64 product_id = 2;
  //分销类型,0：固定，1：比例
  int32 type = 3;
  //分销百分比，单位1%，最大值100，最小值1
  int32 percentage = 4;
  //分销固定金额，单位分
  int32 fixed_amount = 5;
  //是否启用，0=禁用，1=启用
  int32 enable = 6;
}
