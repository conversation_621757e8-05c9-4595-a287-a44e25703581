singleServerConfig:
  idleConnectionTimeout: 10000
  connectTimeout: 10000
  timeout: 3000
  retryAttempts: 3
  retryInterval: 1500
  username: "default"
  password: "kIHlpaEfok5Mr4ICpBj7"
  subscriptionsPerConnection: 5
  clientName: null
  address: "redis://r-2vcundv5wngq97pkfh.redis.cn-chengdu.rds.aliyuncs.com:6379"
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 50
  connectionMinimumIdleSize: 24
  connectionPoolSize: 64
  database: 0
  dnsMonitoringInterval: 5000
codec: !<org.redisson.codec.JsonJacksonCodec> {}
threads: 0
nettyThreads: 0
transportMode: "NIO"