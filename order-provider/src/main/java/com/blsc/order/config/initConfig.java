package com.blsc.order.config;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.oss20190517.AsyncClient;
import com.blsc.cache.commons.RedissonCommon;
import com.blsc.cache.config.RedissonConfig;
import com.blsc.commons.id.IdWorkerCommon;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@Slf4j
public class initConfig {

    @Value("${order.aliyun.access-key-id}")
    private String accessKeyId;
    @Value("${order.aliyun.access-key-secret}")
    private String accessKeySecret;
    @Value("${order.aliyun.oos.region}")
    private String region;
    @Value("${order.aliyun.oos.endpoint}")
    private String endpoint;
    @Value("${order.redis.redisson-name}")
    private String redissonName;

    @Bean
    AsyncClient asyncClient() {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                .build());
        return AsyncClient.builder()
                .region(region) // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(endpoint)
                )
                .build();
    }

    @Bean
    public RedissonCommon redissonCommon() throws IOException {
        RedissonConfig redissonConfig = new RedissonConfig();
        RedissonClient redissonClient = redissonConfig.redissonClient(redissonName);
        return new RedissonCommon(redissonClient);
    }

    @Bean
    public IdWorkerCommon idWorkerCommon(){
        return new IdWorkerCommon(16l);
    }

}
