package com.blsc.order.config;

import com.blsc.payment.PaymentModule;
import com.blsc.payment.config.PaymentConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付策略配置类
 *
 * <AUTHOR>
 * @description 配置支付模块
 * @date 2025-07-29
 */
@Configuration
@Slf4j
public class PaymentModuleConfig {
    
    @Value("${order.pay.wx.appid}")
    private String wechatAppId;
    
    @Value("${order.pay.wx.merchant-id}")
    private String wechatMerchantId;
    
    @Value("${order.pay.wx.pay-notify-url}")
    private String wechatPayNotifyUrl;
    
    @Value("${order.pay.wx.api-v3-key}")
    private String wechatApiKey;
    
    @Value("${order.pay.wx.private-key-path}")
    private String wechatPrivateKeyPath;

    @Value("${order.pay.wx.merchant-serial-number}")
    public String wechatMerchantSerialNumber;
    
    /**
     * 创建支付配置
     */
    @Bean
    public PaymentConfig paymentConfig() {
        PaymentConfig config = new PaymentConfig();
        
        // 配置微信支付
        PaymentConfig.WechatPayConfig wechatConfig = new PaymentConfig.WechatPayConfig();
        wechatConfig.setAppId(wechatAppId);
        wechatConfig.setMerchantId(wechatMerchantId);
        wechatConfig.setPayNotifyUrl(wechatPayNotifyUrl);
        wechatConfig.setApiKey(wechatApiKey);
        wechatConfig.setPrivateKeyPath(wechatPrivateKeyPath);
        wechatConfig.setSerialNumber(wechatMerchantSerialNumber);
        
        config.setWechatPay(wechatConfig);

        // TODO 配置支付宝
        
        return config;
    }
    
    /**
     * 创建支付模块实例
     */
    @Bean
    public PaymentModule paymentModule(PaymentConfig paymentConfig) {
        return new PaymentModule(paymentConfig);
    }

}
