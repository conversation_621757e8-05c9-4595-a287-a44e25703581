syntax = "proto3";

package com.blsc.zt.card.grpc;
option java_multiple_files = true;
option java_package = "com.blsc.zt.card.grpc";
option java_outer_classname = "CardServiceProto";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";


//服务定义
service CardService {
  // 权益分类服务（IBenefitCategoryService）
  rpc GetBenefitCategoryList (google.protobuf.Empty) returns (BenefitCategoryListResponse);

  // 权益信息服务（IBenefitInfoService）
  rpc GetBenefitInfoById (google.protobuf.Int64Value) returns (BenefitInfoResponse);
  rpc GetBenefitList (google.protobuf.Empty) returns (BenefitInfoListResponse);

  // 卡产品服务（ICardProductService）
  rpc GetCardProductByProductId (google.protobuf.Int64Value) returns (CardProductResponse);
  rpc GetCardProductList (QueryCardProductRequest) returns (CardProductPageResponse);

  // 卡权益关联服务（ICardBenefitService）
  rpc GetByCardBenefit (BenefitRequest) returns (CardBenefitResponse);
  rpc GetCardBenefitByProductId (google.protobuf.Int64Value) returns (CardBenefitListResponse);
}

// 通用分页参数
message PageParamsRequest {
  int32 page_num = 1;
  int32 page_size = 2;
  string sort_type = 3;
}

// 通用分页响应
message PageDataResponse {
  int32 page_num = 1;
  int32 page_size = 2;
  int64 total = 3;
  int32 pages = 4;
}

// 权益分类相关消息
message BenefitCategoryRequest {
  int64 category_id = 1;
  string category_name = 2;
  int32 category_order = 3;
}
message AddBenefitCategoryRequest {
  string category_name = 1;
  int32 category_order = 2;
}
message UpdateBenefitCategoryRequest {
  int64 category_id = 1;
  string category_name = 2;
  int32 category_order = 3;
}
message BenefitCategoryListResponse {
  repeated BenefitCategoryRequest categories = 1;
}
message BenefitCategoryPageResponse {
  PageDataResponse page_data = 1;
  repeated BenefitCategoryRequest categories = 2;
}

// 权益信息相关消息
message BenefitInfoRequest {
  int64 benefit_id = 1;
  int64 category_id = 2;
  string benefits_title = 3;
  string benefits_image = 4;
  string benefits_sub_title = 5;
  int32 benefits_stock = 6;
  string benefits_server_type = 7;
  int32 benefits_order = 8;
}
message BenefitInfoResponse {
  int64 benefit_id = 1;
  int64 category_id = 2;
  string benefits_title = 3;
  string benefits_image = 4;
  string benefits_sub_title = 5;
  int32 benefits_stock = 6;
  string benefits_server_type = 7;
  int32 benefits_order = 8;
}
message BenefitInfoListResponse {
  repeated BenefitInfoRequest benefits = 1;
}
message BenefitInfoPageResponse {
  PageDataResponse page_data = 1;
  repeated BenefitInfoRequest benefits = 2;
}

// 权益描述相关消息
message BenefitsDescribeRequest {
  int64 benefit_id = 1;
  string benefit_introduce = 2;
  string benefit_describe = 3;
  string benefit_rule = 4;
  string benefits_phone = 5;
  string benefit_address = 6;
  string benefit_position = 7;
}
message BenefitsDescribeResponse {
  int64 benefit_id = 1;
  string benefit_introduce = 2;
  string benefit_describe = 3;
  string benefit_rule = 4;
  string benefits_phone = 5;
  string benefit_address = 6;
  string benefit_position = 7;
}

// 卡产品相关消息
message CardProductRequest {
  int64 product_id = 1;
  string product_name = 2;
  string product_sub_title = 3;
  int32 product_original_price = 4;
  int32 product_current_price = 5;
  int32 product_effective_time = 6;
  int32 product_order = 7;
  int32 product_type = 8;
  string product_image_url = 9;
  string product_description = 10;
  int32 product_state = 11;
  google.protobuf.Timestamp created_time = 12;
  google.protobuf.Timestamp updated_time = 13;
}
message QueryCardProductRequest {
  int64 product_id = 1;
  string product_name = 2;
  string product_sub_title = 3;
  int32 product_original_price = 4;
  int32 product_current_price = 5;
  int32 product_effective_time = 6;
  int32 product_order = 7;
  int32 product_type = 8;
  string product_image_url = 9;
  string product_description = 10;
  int32 product_state = 11;
  google.protobuf.Timestamp created_time = 12;
  google.protobuf.Timestamp updated_time = 13;
  int32 page_num = 14;
  int32 page_size = 15;
  string sort_type = 16;
}
message CardProductPageResponse {
  PageDataResponse page_data = 1;
  repeated CardProductRequest products = 2;
}
message CardProductResponse {
  int64 product_id = 1;
  string product_name = 2;
  string product_sub_title = 3;
  int32 product_original_price = 4;
  int32 product_current_price = 5;
  int32 product_effective_time = 6;
  int32 product_order = 7;
  int32 product_type = 8;
  string product_image_url = 9;
  string product_description = 10;
  int32 product_state = 11;
  google.protobuf.Timestamp created_time = 12;
  google.protobuf.Timestamp updated_time = 13;

}

// 卡权益关联相关消息
message BenefitRequest {
  int64 product_id = 1;
  int64 benefit_id = 2;
}
message CardBenefitRequest {
  int64 product_id = 1;
  int64 benefit_id = 2;
  bool benefit_optional = 3;
  int32 benefit_limit = 4;
}
message QueryCardBenefitRequest {
  int64 product_id = 1;
  int64 benefit_id = 2;
  bool benefit_optional = 3;
  int32 benefit_limit = 4;
  int32 page_num = 5;
  int32 page_size = 6;
  string sort_type = 7;
}
message CardBenefitPageResponse {
  PageDataResponse page_data = 1;
  repeated CardBenefitRequest cardBenefits = 2;
}
message CardBenefitListResponse {
  repeated CardBenefitRequest cardBenefits = 1;
}
message CardBenefitResponse {
  int64 product_id = 1;
  int64 benefit_id = 2;
  bool benefit_optional = 3;
  int32 benefit_limit = 4;
}

// 通用消息
message IntResponse {
  int32 result = 1; // 0失败，1成功或影响行数
}

