syntax = "proto3";

package com.blsc.order;

import "google/rpc/status.proto";

option java_multiple_files = true;
option java_package = "com.blsc.order.grpc";

service OrderService {
    // 验证订单号
    rpc VerifyOrderNo (VerifyOrderNoRequest) returns (VerifyOrderNoResponse) {};
    // 修改订单
    rpc UpdateOrderStatus (UpdateOrderNoStatusRequest) returns (UpdateOrderNoResponse) {};
    //查询微信支付状态
    rpc QueryWxPayTradeState (QueryWxPayTradeStateRequest) returns (QueryWxPayTradeStateResponse) {};
    //订单号退款
    rpc OrderRefund (OrderRefundRequest) returns (OrderRefundResponse){};
}

message VerifyOrderNoRequest {
    int64 order_no = 1;
    int64 user_id = 2;
}

message VerifyOrderNoResponse {
    int32 status = 1;
}

message UpdateOrderNoStatusRequest {
    int64 order_no = 1;
}

message UpdateOrderNoResponse {
    //0为成功 1为失败
    int32 status = 1;
}

message QueryWxPayTradeStateRequest {
    //订单号
    int64 order_no = 1;
}
// 支付状态枚举
enum TradeState {
    // 未知
    TRADE_STATE_UNKNOWN = 0;
    // 成功
    TRADE_STATE_SUCCESS = 1;
    // 退款
    TRADE_STATE_REFUND = 2;
    // 未支付
    TRADE_STATE_NOT_PAY = 3;
    // 关闭
    TRADE_STATE_CLOSED = 4;
}
message QueryWxPayTradeStateResponse {
    // 内部订单号
    int64 order_no = 1;
    // 微信付款状态
    TradeState trade_state = 2;
    // 付款状态描述
    string trade_state_desc = 3;
    // 付款成功时间
    string trade_state_time = 4;
}

//订单号退款
message OrderRefundRequest {
    //订单号
    int64 order_no = 1;
}
message OrderRefundResponse {
    //业务状态
    google.rpc.Status status = 1;

}