syntax = "proto3";

package com.blsc.grpcapi.ticket;

option java_multiple_files = true;
option java_package = "com.blsc.grpcapi.ticket";

import "google/type/date.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/protobuf/wrappers.proto";

// 提供给用户创建门票订单、获取核销码、退票服务
service TicketService {
  // 获取用户的票的信息
  rpc GetUserTicketInfo (GetUserTicketInfoRequest) returns (GetUserTicketInfoResponse) {}
  // 创建门票订单
  rpc CreateTicketOrder (CreateTicketOrderRequest) returns (CreateTicketOrderResponse) {}
  /**
    出票，获取一个门票订单下多个出行人核销码。该方法耗时可能较长，也可能出现出票失败时，需要重试，所以调用方需要考虑超时和异步处理。
    同时该方法非幂等，核销码的状态会变化
   */
  rpc GetTicketCode (GetTicketCodeRequest) returns (GetTicketCodeResponse) {}
  // 购票出票接口，用于业务方无需关心订单逻辑，直接获取门票的核销码
  rpc GetUserTicketCode (GetUserTicketCodeRequest) returns (GetUserTicketCodeResponse) {}
  // 处理核销码核销回调，更新用户门票状态
  rpc UpdateTicketCodeState (UpdateTicketCodeStateRequest) returns (UpdateTicketCodeStateResponse) {}
  // TODO: 退票
  //  rpc RefundTicket (RefundTicketRequest) returns (RefundTicketResponse) {}
}

// 提供给后台管理， 门票管理相关接口
service TicketManagementService {
  // 管理端创建门票
  rpc CreateTicketInfo (CreateTicketInfoRequest) returns (CreateTicketInfoResponse) {}
  // 管理端修改门票信息
  rpc UpdateTicketInfo (UpdateTicketInfoRequest) returns (UpdateTicketInfoResponse) {}
  // 管理端删除门票
  rpc DeleteTicketInfo (DeleteTicketInfoRequest) returns (DeleteTicketInfoResponse) {}
  // 管理端分页获取门票列表
  rpc ListTicketInfo (ListTicketInfoRequest) returns (ListTicketInfoResponse) {}
}

message TicketOrderRecord {
  int64 order_sn = 1;  // 内部订单号
  string id_card = 2;  // 出行人身份证号
}

message GetUserTicketInfoRequest {
  oneof request_oneof {
    int64 user_ticket_id = 1;  // 用户购票记录编号
    TicketOrderRecord order_record = 2;  // 用户购票记录
  }
}

message GetUserTicketInfoResponse {
  int64 user_ticket_id = 1;  // 用户购票记录编号
  int64 user_id = 2;  // 用户编号
  int64 order_sn = 3;  // 内部订单号
  string name = 4;  // 出行人姓名
  string mobile = 5;  // 出行人手机号
  string id_card = 6;  // 出行人身份证号
  string ticket_title = 7;  // 门票名称
  string ticket_code = 8;  // 门票核销码
  string qr_img = 9;  // 门票二维码图片地址
  int32 check_count = 10;  // 已核销次数
  bool is_used = 11;  // 是否已使用
  TicketCodeStatus code_status = 12;  // 核销码核销状态
  google.protobuf.Timestamp check_time = 13;  // 核销时间
  google.protobuf.Timestamp refund_time = 14;  // 退票时间
}

// 出行人
message Traveler {
  string name = 1;  // 出行人姓名
  string mobile = 2;  // 出行人手机号
  string id_card = 3;  // 出行人身份证号
}

message CreateTicketOrderRequest {
  int64 ticket_id = 1;  // 门票编号
  int64 user_id = 2;  // 购票用户编号
  int32 number = 3;  // 购票数量
  string mobile = 4;  // 购票者手机号
  repeated Traveler travelers = 5;  // 出行人列表
  google.type.Date use_date = 6;  // 使用日期
  int64 order_sn = 7;  // 内部订单号
}

message CreateTicketOrderResponse {
  string out_order_sn = 1;  // 外部票务系统订单号
  int32 scenic_id = 2;  // 票务系统景区 id
  google.type.Date use_date = 3;  // 使用日期
  repeated int64 user_ticket_ids = 4;  // 用户购票记录编号
}

message GetTicketCodeRequest {
  repeated int64 user_ticket_ids = 1;  // 用户购票记录编号
  int64 order_sn = 2;  // 内部订单号
  string out_order_sn = 3;  // 外部订单号
  int32 scenic_id = 4;  // 票务系统景区 id
}

message GetUserTicketCodeRequest {
  int64 user_id = 1;  // 购票用户编号
  int64 ticket_id = 2;  // 门票编号
  int32 number = 3;  // 购票数量
  string mobile = 4;  // 购票者手机号
  repeated Traveler travelers = 5;  // 出行人列表
  google.type.Date use_date = 6;  // 使用日期
}

message TicketCode {
  int64 user_ticket_id = 1;  // 用户购票记录编号
  string code = 2;  // 核销码
  string qr_img = 3;  // 二维码图片地址
  Traveler traveler = 4;  // 出行人信息
  TicketCodeStatus status = 5;  // 核销状态
  google.protobuf.Timestamp check_time = 6;  // 核销时间
  google.protobuf.Timestamp refund_time = 7;  // 退票时间
  int32 check_count = 8;  // 已核销次数
  bool is_used = 9;  // 是否已使用
}

message GetUserTicketCodeResponse {
  repeated TicketCode ticket_codes = 1;  // 绑定到票务系统的核销码列表
}

enum TicketCodeStatus {
  TICKET_CODE_STATUS_UNKNOWN = 0;  // 未知状态
  TICKET_CODE_STATUS_NOT_CHECKED = 1;  // 未核销
  TICKET_CODE_STATUS_CHECKED = 2;  // 已全部核销完成
  TICKET_CODE_STATUS_REFUNDING = 3;  // 退票中
  TICKET_CODE_STATUS_REFUNDED = 4;  // 已退票
}

message GetTicketCodeResponse {
  repeated TicketCode ticket_codes = 1;  // 绑定到票务系统的核销码列表
}

message UpdateTicketCodeStateRequest {
  int64  order_sn = 1;  // 内部订单号
  string out_order_sn = 2;  // 外部订单号
  string code = 3;  // 核销码
  google.protobuf.Timestamp check_time = 4;  // 核销时间
  TicketCodeStatus status = 5;  // 核销状态
  int32 current_check_num = 6;  // 当前核销数量
  int32 valid_num = 7;  // 核销码可核销数量
  int32 check_count = 8;  // 已核销次数
  google.protobuf.BoolValue is_used = 9;  // 是否已使用
}

message UpdateTicketCodeStateResponse {
  google.rpc.Status status = 1;  // 状态
}

enum TicketType {
  TICKET_TYPE_UNKNOWN = 0;  // 未知类型
  TICKET_TYPE_NORMAL = 1;  // 普通票
  TICKET_TYPE_SPECIAL = 2;  // 特惠票
  TICKET_TYPE_ANNUAL = 3;  // 年卡票
}

message TicketInfo {
  int64 ticket_id = 1;  // 门票编号
  int32 scenic_id = 2;  // 票务系统景区 id
  int32 out_ticket_id = 3;  // 票务系统票种 id
  string title = 4;  // 门票名称
  string subtitle = 5;  // 门票副标题
  TicketType type = 6;  // 门票类型
  int32 price = 7;  // 门票价格, 单位分
  string logo_url = 8;  // 门票 logo 地址
  string introduction = 9;  // 门票介绍
  string announcement = 10;  // 门票公告
  string explanation = 11;  // 门票特别说明
  string rule = 12;  // 门票使用规则
  string tips = 13;  // 门票温馨提示
  google.protobuf.BoolValue is_real_name = 14;  // 是否需要实名制
  google.protobuf.BoolValue is_need_mobile = 15;  // 是否需要手机号
  google.protobuf.BoolValue is_time_duration = 16;  // 是否需要按时间段购票
  google.type.Date start_date = 17;  // 门票有效期开始日期
  google.type.Date end_date = 18;  // 门票有效期结束日期
  google.protobuf.BoolValue is_valid = 19;  // 门票状态, 0-下架，1-上架
  int32 sort_num = 20;  // 排序编号, 数字越大越靠前
}

message CreateTicketInfoRequest {
  TicketInfo ticket = 1;  // 门票信息
}

message CreateTicketInfoResponse {
  int64 ticket_id = 1;  // 门票编号
}

message UpdateTicketInfoRequest {
  TicketInfo ticket = 1;  // 要修改的门票信息
}

message UpdateTicketInfoResponse {
  google.rpc.Status status = 1;  // 结果状态
}

message DeleteTicketInfoRequest {
  int64 ticket_id = 1;  // 要删除的门票编号
}

message DeleteTicketInfoResponse {
  google.rpc.Status status = 1;  // 结果状态
}

message ListTicketInfoRequest {
  int32 page_num = 1;  // 当前页码
  int32 page_size = 2;  // 每页数量
}

message ListTicketInfoResponse {
  int64 total_size = 1;  // 总数量
  int32 page_num = 2;   // 当前页码
  repeated TicketInfo tickets = 3;  // 门票列表
}
