syntax = "proto3";

package com.blsc.grpcapi.combination;

option java_multiple_files = true;
option java_package = "com.blsc.grpcapi.combination";

import "google/rpc/status.proto";

// 拼团服务
service CombinationService {
  // 开团/参团, 团长开团, 团员参团
  rpc JoinCombinationGroup (JoinCombinationGroupRequest) returns (JoinCombinationGroupResponse) {}
  // 根据产品唯一标识获取拼团商品概要信息
  rpc GetCombinationProduct (GetCombinationProductRequest) returns (GetCombinationProductResponse) {}
}

/**
  开团调用案例：
  {
    "app_id": "mini:nk",
    "order_sn": 11224455,
    "count": 1,
    "activity_id": 1,
    "product_id": 1000,
    "user_id": 95962434955963,
    "head_id": 0
  }
  表示 95962434955963 作为团长开团

  参与拼团调用案例：
  {
    "app_id": "mini:nk",
    "order_sn": 11224466,
    "count": 1,
    "activity_id": 1,
    "product_id": 1000,
    "user_id": 95962434955964,
    "head_id": 95962434955963
  }
  表示 95962434955964 用户参与到 95962434955963 用户开的团中
 */
message JoinCombinationGroupRequest {
  string app_id = 1; // 业务应用编号
  int64 order_sn = 2; // 订单编号
  int32 count = 3; // 购买数量
  int64 activity_id = 4; // 拼团活动编号
  int64 product_id = 5; // 商品编号
  int64 user_id = 6; // 用户编号
  int64 head_id = 7; // 团长用户编号, 0 表示是团长开团
}

message JoinCombinationGroupResponse {
  google.rpc.Status status = 1; // 接口成功状态
}

message GetCombinationProductRequest {
  string app_id = 1; // 业务应用编号
  int64 product_id = 2; // 商品编号
}

message GetCombinationProductResponse {
  int64 product_id = 1; // 商品编号
  string product_name = 2; // 商品名称
  int32 combination_price = 3; // 拼团价格
  int32 original_price = 4; // 原价(营销价)
}