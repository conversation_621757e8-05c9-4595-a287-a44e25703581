# Payment模块退款功能使用指南

## 概述

Payment模块现已支持完整的退款功能，包括退款申请、退款查询、退款回调验证和处理。本文档介绍如何使用这些功能。

## 功能特性

- ✅ 退款申请：支持全额退款和部分退款
- ✅ 退款查询：查询退款状态和详细信息
- ✅ 退款回调验证：验证微信支付退款回调的签名
- ✅ 退款回调处理：处理退款结果通知
- ✅ 多渠道支持：保持支付策略的通用性，支持扩展其他支付渠道
- ✅ 错误处理：完善的异常处理和错误码定义

## 配置说明

### 微信支付配置

在`PaymentConfig.WechatPayConfig`中添加退款回调URL配置：

```java
PaymentConfig.WechatPayConfig wechatConfig = new PaymentConfig.WechatPayConfig();
wechatConfig.setAppId("your_app_id");
wechatConfig.setMerchantId("your_merchant_id");
wechatConfig.setPayNotifyUrl("https://your-domain.com/payment/callback");
wechatConfig.setRefundNotifyUrl("https://your-domain.com/refund/callback"); // 新增
wechatConfig.setApiKey("your_api_key");
wechatConfig.setCertPath("path/to/cert.pem");
wechatConfig.setPrivateKeyPath("path/to/private_key.pem");
wechatConfig.setSerialNumber("certificate_serial_number");
```

## 使用方法

### 1. 申请退款

```java
// 构建退款请求
RefundRequest refundRequest = RefundRequest.builder()
    .orderNo(1234567890L)                    // 原订单号
    .refundNo("REFUND_" + System.currentTimeMillis()) // 商户退款单号（唯一）
    .paymentMethod(PaymentMethod.WECHAT_PAY) // 支付方式
    .refundAmount(1000)                      // 退款金额（分）
    .totalAmount(2000)                       // 原订单总金额（分）
    .reason("用户申请退款")                   // 退款原因
    .notifyUrl("https://your-domain.com/refund/callback") // 可选：退款回调URL
    .fundsAccount("AVAILABLE")               // 可选：退款资金来源
    .build();

// 申请退款
try {
    RefundResponse response = paymentModule.refund(refundRequest);
    
    if (response.isSuccess()) {
        System.out.println("退款申请成功");
        System.out.println("微信退款单号: " + response.getRefundId());
        System.out.println("退款状态: " + response.getRefundStatus());
    } else {
        System.out.println("退款申请失败: " + response.getErrorMessage());
    }
} catch (PaymentException e) {
    System.out.println("退款申请异常: " + e.getFullErrorMessage());
}
```

### 2. 查询退款状态

```java
// 构建退款查询请求
RefundQueryRequest queryRequest = RefundQueryRequest.builder()
    .orderNo(1234567890L)                    // 原订单号
    .refundNo("REFUND_1234567890")           // 商户退款单号
    .paymentMethod(PaymentMethod.WECHAT_PAY) // 支付方式
    .build();

// 查询退款状态
try {
    RefundQueryResponse response = paymentModule.queryRefund(queryRequest);
    
    if (response.isSuccess()) {
        System.out.println("退款查询成功");
        System.out.println("退款状态: " + response.getRefundStatus());
        System.out.println("退款状态描述: " + response.getRefundStatusDesc());
        
        if (response.getAmount() != null) {
            System.out.println("退款金额: " + response.getAmount().getRefund() + "分");
        }
    } else {
        System.out.println("退款查询失败: " + response.getErrorMessage());
    }
} catch (PaymentException e) {
    System.out.println("退款查询异常: " + e.getFullErrorMessage());
}
```

### 3. 处理退款回调

```java
@PostMapping("/refund/callback")
public ResponseEntity<String> handleRefundCallback(
        HttpServletRequest request,
        @RequestBody String body) {
    
    try {
        // 获取请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Wechatpay-Serial", request.getHeader("Wechatpay-Serial"));
        headers.put("Wechatpay-Nonce", request.getHeader("Wechatpay-Nonce"));
        headers.put("Wechatpay-Timestamp", request.getHeader("Wechatpay-Timestamp"));
        headers.put("Wechatpay-Signature", request.getHeader("Wechatpay-Signature"));
        
        // 验证回调签名
        boolean isValid = paymentModule.validateRefundCallback(
            PaymentMethod.WECHAT_PAY, headers, body);
        
        if (!isValid) {
            return ResponseEntity.status(400).body("{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}");
        }
        
        // 处理退款回调
        RefundCallbackResult result = paymentModule.processRefundCallback(
            PaymentMethod.WECHAT_PAY, headers, body);
        
        if (result.isSuccess()) {
            // 更新业务系统中的退款状态
            updateRefundStatus(result);
            
            // 返回成功响应
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.status(500).body("{\"code\":\"FAIL\",\"message\":\"处理失败\"}");
        }
        
    } catch (Exception e) {
        log.error("处理退款回调失败", e);
        return ResponseEntity.status(500).body("{\"code\":\"FAIL\",\"message\":\"系统错误\"}");
    }
}
```

## 退款状态说明

### 微信支付退款状态

- `SUCCESS`: 退款成功
- `CLOSED`: 退款关闭
- `PROCESSING`: 退款处理中
- `ABNORMAL`: 退款异常

### 退款渠道

- `ORIGINAL`: 原路退款
- `BALANCE`: 退回到余额
- `OTHER_BALANCE`: 原账户异常退到其他余额账户
- `OTHER_BANKCARD`: 原银行卡异常退到其他银行卡

## 错误处理

### 错误类型

- `REFUND_FAILED`: 退款失败
- `REFUND_QUERY_FAILED`: 退款查询失败
- `REFUND_CALLBACK_FAILED`: 退款回调处理失败
- `REFUND_AMOUNT_ERROR`: 退款金额错误
- `REFUND_NO_DUPLICATE`: 退款单号重复

### 异常处理示例

```java
try {
    RefundResponse response = paymentModule.refund(refundRequest);
    // 处理成功逻辑
} catch (PaymentException e) {
    switch (e.getErrorCode()) {
        case "REFUND_AMOUNT_ERROR":
            // 处理金额错误
            break;
        case "REFUND_NO_DUPLICATE":
            // 处理退款单号重复
            break;
        default:
            // 处理其他错误
            break;
    }
}
```

## 注意事项

1. **退款单号唯一性**: 商户退款单号必须在商户系统内唯一
2. **退款金额限制**: 退款金额不能超过原订单金额
3. **退款次数限制**: 一笔订单最多支持50次部分退款
4. **退款时效**: 交易完成后一年内可申请退款
5. **回调处理**: 退款回调必须在5秒内响应，否则微信会重复发送
6. **签名验证**: 必须验证退款回调的签名，确保数据安全

## 扩展其他支付渠道

要支持其他支付渠道的退款功能，只需：

1. 实现`PaymentStrategy`接口的退款相关方法
2. 在`PaymentStrategyFactory`中注册新的策略
3. 添加相应的配置参数

示例：

```java
public class AlipayPaymentStrategy implements PaymentStrategy {
    
    @Override
    public RefundResponse refund(RefundRequest refundRequest) throws PaymentException {
        // 实现支付宝退款逻辑
    }
    
    @Override
    public RefundQueryResponse queryRefund(RefundQueryRequest queryRequest) throws PaymentException {
        // 实现支付宝退款查询逻辑
    }
    
    // 实现其他方法...
}
```

## 完整示例

参考 `com.blsc.payment.example.RefundExample` 类，查看完整的使用示例。
