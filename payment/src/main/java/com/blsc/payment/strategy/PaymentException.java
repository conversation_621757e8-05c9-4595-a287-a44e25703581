package com.blsc.payment.strategy;

import com.blsc.payment.model.PaymentErrorType;
import com.blsc.payment.model.PaymentMethod;
import lombok.Getter;

/**
 * 支付异常类
 *
 * <AUTHOR>
 * @description 支付相关的业务异常，包含支付上下文信息
 * @date 2025/7/29
 */
@Getter
public class PaymentException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误信息
     */
    private final String errorMessage;

    /**
     * 订单号
     */
    private final Long orderNo;

    /**
     * 交易ID（第三方支付平台的交易号）
     */
    private final String transactionId;

    /**
     * 支付金额（分）
     */
    private final Integer amount;

    /**
     * 支付方式
     */
    private final PaymentMethod paymentMethod;

    /**
     * 完整构造函数
     */
    public PaymentException(String errorCode, String errorMessage,
                            Long orderNo, String transactionId, Integer amount, PaymentMethod paymentMethod, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.orderNo = orderNo;
        this.transactionId = transactionId;
        this.amount = amount;
        this.paymentMethod = paymentMethod;
    }

    /**
     * 基础构造函数
     */
    public PaymentException(String errorCode, String errorMessage) {
        this(errorCode, errorMessage, null, null, null, null, null);
    }

    /**
     * 带异常原因的构造函数
     */
    public PaymentException(String errorCode, String errorMessage, Throwable cause) {
        this(errorCode, errorMessage, null, null, null, null, cause);
    }

    /**
     * 带订单信息的构造函数
     */
    public PaymentException(String errorCode, String errorMessage, Long orderNo, PaymentMethod paymentMethod) {
        this(errorCode, errorMessage, orderNo, null, null, paymentMethod, null);
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建参数验证异常
     */
    public static PaymentException validationError(String message, Long orderNo, PaymentMethod paymentMethod) {
        return new PaymentException(PaymentErrorType.VALIDATION_ERROR.getCode(), message, orderNo, paymentMethod);
    }

    /**
     * 创建支付失败异常
     */
    public static PaymentException paymentFailed(String message, Long orderNo, PaymentMethod paymentMethod, Throwable cause) {
        return new PaymentException(PaymentErrorType.PAYMENT_FAILED.getCode(), message, orderNo, null, null, paymentMethod, cause);
    }

    /**
     * 创建支付超时异常
     */
    public static PaymentException paymentTimeout(Long orderNo, PaymentMethod paymentMethod) {
        return new PaymentException(PaymentErrorType.PAYMENT_TIMEOUT.getCode(), "支付超时", orderNo, paymentMethod);
    }

    /**
     * 创建回调处理失败异常
     */
    public static PaymentException callbackFailed(String message, String transactionId, PaymentMethod paymentMethod, Throwable cause) {
        return new PaymentException(PaymentErrorType.CALLBACK_FAILED.getCode(), message,
                null, transactionId, null, paymentMethod, cause);
    }

    /**
     * 创建系统错误异常
     */
    public static PaymentException systemError(String message, Throwable cause) {
        return new PaymentException(PaymentErrorType.SYSTEM_ERROR.getCode(), message,
                null, null, null, null, cause);
    }

    // ==================== 实用方法 ====================

    // ==================== 实用方法 ====================

    /**
     * 获取完整的错误信息（包含上下文）
     */
    public String getFullErrorMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("支付异常: ").append(errorMessage);

        if (orderNo != null) {
            sb.append(", 订单号: ").append(orderNo);
        }

        if (transactionId != null) {
            sb.append(", 交易号: ").append(transactionId);
        }

        if (paymentMethod != null) {
            sb.append(", 支付方式: ").append(paymentMethod.getDescription());
        }

        if (amount != null) {
            sb.append(", 金额: ").append(amount).append("分");
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return getFullErrorMessage();
    }

}