package com.blsc.payment.strategy;


import com.blsc.payment.dto.*;
import com.blsc.payment.model.PaymentCallbackResult;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.model.RefundCallbackResult;

import java.util.Map;

/**
 * 支付策略接口
 * 
 * <AUTHOR>
 * @description 定义支付策略的通用接口，支持策略模式实现不同支付方式
 * @date 2025/7/29
 */
public interface PaymentStrategy {
    
    /**
     * 获取支持的支付方式
     * 
     * @return 支付方式枚举
     */
    PaymentMethod getSupportedPaymentMethod();
    
    /**
     * 创建支付订单
     * 
     * @param paymentRequest 支付请求参数
     * @return 支付响应结果
     * @throws PaymentException 支付异常
     */
    PaymentResponse createPayment(PaymentRequest paymentRequest) throws PaymentException;
    
    /**
     * 查询支付状态
     * 
     * @param queryRequest 查询请求参数
     * @return 查询响应结果
     * @throws PaymentException 支付异常
     */
    PaymentQueryResponse queryPayment(PaymentQueryRequest queryRequest) throws PaymentException;
    
    /**
     * 验证支付回调
     * 
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 是否验证成功
     * @throws PaymentException 支付异常
     */
    boolean validateCallback(Map<String, String> headers, String body) throws PaymentException;
    
    /**
     * 处理支付回调
     *
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 回调处理结果
     * @throws PaymentException 支付异常
     */
    PaymentCallbackResult processCallback(Map<String, String> headers, String body) throws PaymentException;

    /**
     * 申请退款
     *
     * @param refundRequest 退款请求参数
     * @return 退款响应结果
     * @throws PaymentException 支付异常
     */
    RefundResponse refund(RefundRequest refundRequest) throws PaymentException;

    /**
     * 查询退款状态
     *
     * @param queryRequest 退款查询请求参数
     * @return 退款查询响应结果
     * @throws PaymentException 支付异常
     */
    RefundQueryResponse queryRefund(RefundQueryRequest queryRequest) throws PaymentException;

    /**
     * 验证退款回调
     *
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 是否验证成功
     * @throws PaymentException 支付异常
     */
    boolean validateRefundCallback(Map<String, String> headers, String body) throws PaymentException;

    /**
     * 处理退款回调
     *
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 退款回调处理结果
     * @throws PaymentException 支付异常
     */
    RefundCallbackResult processRefundCallback(Map<String, String> headers, String body) throws PaymentException;
    
    /**
     * 检查策略是否可用
     * 
     * @return 是否可用
     */
    default boolean isAvailable() {
        return true;
    }
    
    /**
     * 获取策略优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
