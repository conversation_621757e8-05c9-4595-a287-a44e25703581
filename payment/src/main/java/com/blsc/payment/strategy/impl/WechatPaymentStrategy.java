package com.blsc.payment.strategy.impl;

import com.blsc.payment.dto.PaymentRequest;
import com.blsc.payment.dto.PaymentResponse;
import com.blsc.payment.dto.PaymentQueryRequest;
import com.blsc.payment.dto.PaymentQueryResponse;
import com.blsc.payment.model.PaymentCallbackResult;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.strategy.PaymentException;
import com.blsc.payment.strategy.PaymentStrategy;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付策略实现
 * 
 * <AUTHOR>
 * @description 微信支付的具体实现策略
 * @date 2025-07-29
 */
@Slf4j
public class WechatPaymentStrategy implements PaymentStrategy {
    
    private final String appId;
    private final String merchantId;
    private final String payNotifyUrl;
    private final JsapiServiceExtension jsapiServiceExtension;
    private final NotificationParser notificationParser;

    /**
     * 构造函数注入依赖
     */
    public WechatPaymentStrategy(String appId,
                                String merchantId,
                                String payNotifyUrl,
                                JsapiServiceExtension jsapiServiceExtension,
                                NotificationParser notificationParser) {
        this.appId = appId;
        this.merchantId = merchantId;
        this.payNotifyUrl = payNotifyUrl;
        this.jsapiServiceExtension = jsapiServiceExtension;
        this.notificationParser = notificationParser;
    }
    
    @Override
    public PaymentMethod getSupportedPaymentMethod() {
        return PaymentMethod.WECHAT_PAY;
    }
    
    @Override
    public PaymentResponse createPayment(PaymentRequest paymentRequest) throws PaymentException {
        log.info("创建微信支付订单，订单号: {}, 金额: {}", 
                paymentRequest.getOrderNo(), paymentRequest.getAmount());
        
        try {
            // 验证微信支付请求参数
            validateWechatPaymentRequest(paymentRequest);

            // 构建微信支付请求
            PrepayRequest prepayRequest = buildWechatPrepayRequest(paymentRequest);
            
            // 调用微信支付API
            PrepayWithRequestPaymentResponse wxResponse = jsapiServiceExtension.prepayWithRequestPayment(prepayRequest);
            
            // 构建支付参数
            Map<String, Object> paymentParams = buildPaymentParams(wxResponse);
            
            log.info("微信支付订单创建成功，订单号: {}", paymentRequest.getOrderNo());
            
            return PaymentResponse.success(
                    paymentRequest.getOrderNo(),
                    paymentParams
            );
            
        } catch (Exception e) {
            log.error("创建微信支付订单失败，订单号: {}", paymentRequest.getOrderNo(), e);
            throw PaymentException.paymentFailed(
                "创建微信支付订单失败: " + e.getMessage(),
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public PaymentQueryResponse queryPayment(PaymentQueryRequest queryRequest) throws PaymentException {
        log.info("查询微信支付状态，订单号: {}", queryRequest.getOrderNo());
        
        try {
            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
            request.setMchid(merchantId);
            request.setOutTradeNo(queryRequest.getOrderNo().toString());
            
            Transaction transaction = jsapiServiceExtension.queryOrderByOutTradeNo(request);
            
            LocalDateTime successTime = null;
            if (transaction.getSuccessTime() != null) {
                successTime = parseWechatTime(transaction.getSuccessTime());
            }
            
            log.info("微信支付状态查询成功，订单号: {}, 状态: {}", 
                    queryRequest.getOrderNo(), transaction.getTradeState());
            
            return PaymentQueryResponse.success(
                    queryRequest.getOrderNo(),
                    transaction.getTransactionId(),
                    transaction.getTradeState().toString(),
                    transaction.getTradeStateDesc(),
                    successTime
            );
            
        } catch (Exception e) {
            log.error("查询微信支付状态失败，订单号: {}", queryRequest.getOrderNo(), e);
            throw PaymentException.paymentFailed(
                "查询微信支付状态失败: " + e.getMessage(),
                queryRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public boolean validateCallback(Map<String, String> headers, String body) throws PaymentException {
        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            // 微信支付SDK会自动验证签名
            Transaction transaction = notificationParser.parse(requestParam, Transaction.class);
            return transaction != null;
            
        } catch (Exception e) {
            log.error("验证微信支付回调失败", e);
            return false;
        }
    }
    
    @Override
    public PaymentCallbackResult processCallback(Map<String, String> headers, String body) throws PaymentException {
        log.info("处理微信支付回调, headers: {}, body: {}", headers, body);
        
        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            Transaction transaction = notificationParser.parse(requestParam, Transaction.class);
            
            Long orderNo = Long.valueOf(transaction.getOutTradeNo());
            String transactionId = transaction.getTransactionId();
            String paymentStatus = transaction.getTradeState().toString();
            
            LocalDateTime paymentTime = null;
            if (transaction.getSuccessTime() != null) {
                paymentTime = parseWechatTime(transaction.getSuccessTime());
            }
            
            Integer totalAmount = null;
            if (transaction.getAmount() != null) {
                totalAmount = transaction.getAmount().getTotal();
            }
            
            log.info("微信支付回调处理成功，订单号: {}, 交易号: {}, 状态: {}", 
                    orderNo, transactionId, paymentStatus);
            
            return PaymentCallbackResult.success(
                    orderNo,
                    transactionId,
                    paymentStatus,
                    paymentTime,
                    totalAmount
            );
            
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            throw PaymentException.callbackFailed(
                "处理微信支付回调失败: " + e.getMessage(),
                null,
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public boolean isAvailable() {
        return jsapiServiceExtension != null && 
               appId != null && !appId.trim().isEmpty() && 
               merchantId != null && !merchantId.trim().isEmpty();
    }
    
    @Override
    public int getPriority() {
        return 10; // 微信支付优先级较高
    }
    
    /**
     * 验证微信支付请求参数
     */
    private void validateWechatPaymentRequest(PaymentRequest paymentRequest) throws PaymentException {
        if ((paymentRequest.getOpenId() == null || paymentRequest.getOpenId().trim().isEmpty())
            && (paymentRequest.getUserId() == null)) {
            throw PaymentException.validationError(
                "微信支付需要用户OpenID或用户ID",
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }

        if (paymentRequest.getProductDescription() == null || paymentRequest.getProductDescription().trim().isEmpty()) {
            throw PaymentException.validationError(
                "商品描述不能为空",
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }
    }
    
    /**
     * 构建微信支付预支付请求
     */
    private PrepayRequest buildWechatPrepayRequest(PaymentRequest paymentRequest) {
        PrepayRequest prepayRequest = new PrepayRequest();
        prepayRequest.setAppid(appId);
        prepayRequest.setMchid(merchantId);
        prepayRequest.setDescription(paymentRequest.getProductDescription());
        prepayRequest.setOutTradeNo(paymentRequest.getOrderNo().toString());
        prepayRequest.setNotifyUrl(payNotifyUrl);
        
        // 设置金额
        Amount amount = new Amount();
        amount.setTotal(paymentRequest.getAmount());
        amount.setCurrency("CNY");
        prepayRequest.setAmount(amount);
        
        // 设置支付者信息
        Payer payer = new Payer();
        payer.setOpenid(paymentRequest.getOpenId());
        prepayRequest.setPayer(payer);
        
        return prepayRequest;
    }
    
    /**
     * 构建支付参数
     */
    private Map<String, Object> buildPaymentParams(PrepayWithRequestPaymentResponse wxResponse) {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", wxResponse.getAppId());
        params.put("timeStamp", wxResponse.getTimeStamp());
        params.put("nonceStr", wxResponse.getNonceStr());
        params.put("package", wxResponse.getPackageVal());
        params.put("signType", wxResponse.getSignType());
        params.put("paySign", wxResponse.getPaySign());
        return params;
    }
    
    /**
     * 解析微信时间格式
     */
    private LocalDateTime parseWechatTime(String timeStr) {
        try {
            return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00"));
        } catch (Exception e) {
            log.warn("解析微信时间格式失败: {}", timeStr, e);
            return null;
        }
    }
    
    /**
     * 转换回调参数
     */
    private RequestParam convertToRequestParam(Map<String, String> headers, String body) {
        // 这里需要实现具体的转换逻辑
        // 简化实现，实际需要根据微信支付回调的具体格式来转换
        return new RequestParam.Builder()
                .serialNumber(headers.get("Wechatpay-Serial"))
                .nonce(headers.get("Wechatpay-Nonce"))
                .timestamp(headers.get("Wechatpay-Timestamp"))
                .signature(headers.get("Wechatpay-Signature"))
                .body(body)
                .build();
    }
    
    /**
     * 用户信息服务接口
     */
//    public interface UserInfoService {
//        String getOpenIdByUserId(Long userId);
//    }
}
