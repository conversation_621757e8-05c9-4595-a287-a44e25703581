package com.blsc.payment.strategy.impl;

import com.blsc.payment.dto.*;
import com.blsc.payment.model.PaymentCallbackResult;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.model.RefundCallbackResult;
import com.blsc.payment.strategy.PaymentException;
import com.blsc.payment.strategy.PaymentStrategy;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信支付策略实现
 * 
 * <AUTHOR>
 * @description 微信支付的具体实现策略
 * @date 2025-07-29
 */
@Slf4j
public class WechatPaymentStrategy implements PaymentStrategy {
    
    private final String appId;
    private final String merchantId;
    private final String payNotifyUrl;
    private final String refundNotifyUrl;
    private final JsapiServiceExtension jsapiServiceExtension;
    private final RefundService refundService;
    private final NotificationParser notificationParser;

    /**
     * 构造函数注入依赖
     */
    public WechatPaymentStrategy(String appId,
                                String merchantId,
                                String payNotifyUrl,
                                String refundNotifyUrl,
                                JsapiServiceExtension jsapiServiceExtension,
                                NotificationParser notificationParser) {
        this.appId = appId;
        this.merchantId = merchantId;
        this.payNotifyUrl = payNotifyUrl;
        this.refundNotifyUrl = refundNotifyUrl;
        this.jsapiServiceExtension = jsapiServiceExtension;
        // 创建退款服务，使用与支付服务相同的配置
        this.refundService = new RefundService.Builder()
            .config(jsapiServiceExtension.getConfig())
            .build();
        this.notificationParser = notificationParser;
    }
    
    @Override
    public PaymentMethod getSupportedPaymentMethod() {
        return PaymentMethod.WECHAT_PAY;
    }
    
    @Override
    public PaymentResponse createPayment(PaymentRequest paymentRequest) throws PaymentException {
        log.info("创建微信支付订单，订单号: {}, 金额: {}", 
                paymentRequest.getOrderNo(), paymentRequest.getAmount());
        
        try {
            // 验证微信支付请求参数
            validateWechatPaymentRequest(paymentRequest);

            // 构建微信支付请求
            PrepayRequest prepayRequest = buildWechatPrepayRequest(paymentRequest);
            
            // 调用微信支付API
            PrepayWithRequestPaymentResponse wxResponse = jsapiServiceExtension.prepayWithRequestPayment(prepayRequest);
            
            // 构建支付参数
            Map<String, Object> paymentParams = buildPaymentParams(wxResponse);
            
            log.info("微信支付订单创建成功，订单号: {}", paymentRequest.getOrderNo());
            
            return PaymentResponse.success(
                    paymentRequest.getOrderNo(),
                    paymentParams
            );
            
        } catch (Exception e) {
            log.error("创建微信支付订单失败，订单号: {}", paymentRequest.getOrderNo(), e);
            throw PaymentException.paymentFailed(
                "创建微信支付订单失败: " + e.getMessage(),
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public PaymentQueryResponse queryPayment(PaymentQueryRequest queryRequest) throws PaymentException {
        log.info("查询微信支付状态，订单号: {}", queryRequest.getOrderNo());
        
        try {
            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
            request.setMchid(merchantId);
            request.setOutTradeNo(queryRequest.getOrderNo().toString());
            
            Transaction transaction = jsapiServiceExtension.queryOrderByOutTradeNo(request);
            
            LocalDateTime successTime = null;
            if (transaction.getSuccessTime() != null) {
                successTime = parseWechatTime(transaction.getSuccessTime());
            }
            
            log.info("微信支付状态查询成功，订单号: {}, 状态: {}", 
                    queryRequest.getOrderNo(), transaction.getTradeState());
            
            return PaymentQueryResponse.success(
                    queryRequest.getOrderNo(),
                    transaction.getTransactionId(),
                    transaction.getTradeState().toString(),
                    transaction.getTradeStateDesc(),
                    successTime
            );
            
        } catch (Exception e) {
            log.error("查询微信支付状态失败，订单号: {}", queryRequest.getOrderNo(), e);
            throw PaymentException.paymentFailed(
                "查询微信支付状态失败: " + e.getMessage(),
                queryRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public boolean validateCallback(Map<String, String> headers, String body) throws PaymentException {
        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            // 微信支付SDK会自动验证签名
            Transaction transaction = notificationParser.parse(requestParam, Transaction.class);
            return transaction != null;
            
        } catch (Exception e) {
            log.error("验证微信支付回调失败", e);
            return false;
        }
    }
    
    @Override
    public PaymentCallbackResult processCallback(Map<String, String> headers, String body) throws PaymentException {
        log.info("处理微信支付回调, headers: {}, body: {}", headers, body);
        
        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            Transaction transaction = notificationParser.parse(requestParam, Transaction.class);
            
            Long orderNo = Long.valueOf(transaction.getOutTradeNo());
            String transactionId = transaction.getTransactionId();
            String paymentStatus = transaction.getTradeState().toString();
            
            LocalDateTime paymentTime = null;
            if (transaction.getSuccessTime() != null) {
                paymentTime = parseWechatTime(transaction.getSuccessTime());
            }
            
            Integer totalAmount = null;
            if (transaction.getAmount() != null) {
                totalAmount = transaction.getAmount().getTotal();
            }
            
            log.info("微信支付回调处理成功，订单号: {}, 交易号: {}, 状态: {}", 
                    orderNo, transactionId, paymentStatus);
            
            return PaymentCallbackResult.success(
                    orderNo,
                    transactionId,
                    paymentStatus,
                    paymentTime,
                    totalAmount
            );
            
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            throw PaymentException.callbackFailed(
                "处理微信支付回调失败: " + e.getMessage(),
                null,
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }
    
    @Override
    public boolean isAvailable() {
        return jsapiServiceExtension != null && 
               appId != null && !appId.trim().isEmpty() && 
               merchantId != null && !merchantId.trim().isEmpty();
    }
    
    @Override
    public int getPriority() {
        return 10; // 微信支付优先级较高
    }
    
    /**
     * 验证微信支付请求参数
     */
    private void validateWechatPaymentRequest(PaymentRequest paymentRequest) throws PaymentException {
        if ((paymentRequest.getOpenId() == null || paymentRequest.getOpenId().trim().isEmpty())
            && (paymentRequest.getUserId() == null)) {
            throw PaymentException.validationError(
                "微信支付需要用户OpenID或用户ID",
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }

        if (paymentRequest.getProductDescription() == null || paymentRequest.getProductDescription().trim().isEmpty()) {
            throw PaymentException.validationError(
                "商品描述不能为空",
                paymentRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }
    }
    
    /**
     * 构建微信支付预支付请求
     */
    private PrepayRequest buildWechatPrepayRequest(PaymentRequest paymentRequest) {
        PrepayRequest prepayRequest = new PrepayRequest();
        prepayRequest.setAppid(appId);
        prepayRequest.setMchid(merchantId);
        prepayRequest.setDescription(paymentRequest.getProductDescription());
        prepayRequest.setOutTradeNo(paymentRequest.getOrderNo().toString());
        prepayRequest.setNotifyUrl(payNotifyUrl);
        
        // 设置金额
        com.wechat.pay.java.service.payments.jsapi.model.Amount amount =
            new com.wechat.pay.java.service.payments.jsapi.model.Amount();
        amount.setTotal(paymentRequest.getAmount());
        amount.setCurrency("CNY");
        prepayRequest.setAmount(amount);
        
        // 设置支付者信息
        Payer payer = new Payer();
        payer.setOpenid(paymentRequest.getOpenId());
        prepayRequest.setPayer(payer);
        
        return prepayRequest;
    }
    
    /**
     * 构建支付参数
     */
    private Map<String, Object> buildPaymentParams(PrepayWithRequestPaymentResponse wxResponse) {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", wxResponse.getAppId());
        params.put("timeStamp", wxResponse.getTimeStamp());
        params.put("nonceStr", wxResponse.getNonceStr());
        params.put("package", wxResponse.getPackageVal());
        params.put("signType", wxResponse.getSignType());
        params.put("paySign", wxResponse.getPaySign());
        return params;
    }
    
    /**
     * 解析微信时间格式
     */
    private LocalDateTime parseWechatTime(String timeStr) {
        try {
            return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00"));
        } catch (Exception e) {
            log.warn("解析微信时间格式失败: {}", timeStr, e);
            return null;
        }
    }
    
    /**
     * 转换回调参数
     */
    private RequestParam convertToRequestParam(Map<String, String> headers, String body) {
        // 这里需要实现具体的转换逻辑
        // 简化实现，实际需要根据微信支付回调的具体格式来转换
        return new RequestParam.Builder()
                .serialNumber(headers.get("Wechatpay-Serial"))
                .nonce(headers.get("Wechatpay-Nonce"))
                .timestamp(headers.get("Wechatpay-Timestamp"))
                .signature(headers.get("Wechatpay-Signature"))
                .body(body)
                .build();
    }
    
    @Override
    public RefundResponse refund(RefundRequest refundRequest) throws PaymentException {
        log.info("申请微信支付退款，订单号: {}, 退款单号: {}, 退款金额: {}",
                refundRequest.getOrderNo(), refundRequest.getRefundNo(), refundRequest.getRefundAmount());

        try {
            // 验证退款请求参数
            validateWechatRefundRequest(refundRequest);

            // 构建微信退款请求
            CreateRequest createRequest = buildWechatRefundRequest(refundRequest);

            // 调用微信退款API
            Refund wxRefund = refundService.create(createRequest);

            // 构建退款响应
            RefundResponse response = buildRefundResponse(wxRefund, refundRequest);

            log.info("微信支付退款申请成功，订单号: {}, 退款单号: {}, 微信退款单号: {}",
                    refundRequest.getOrderNo(), refundRequest.getRefundNo(), wxRefund.getRefundId());

            return response;

        } catch (Exception e) {
            log.error("申请微信支付退款失败，订单号: {}, 退款单号: {}",
                    refundRequest.getOrderNo(), refundRequest.getRefundNo(), e);
            throw PaymentException.refundFailed(
                "申请微信支付退款失败: " + e.getMessage(),
                refundRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }

    @Override
    public RefundQueryResponse queryRefund(RefundQueryRequest queryRequest) throws PaymentException {
        log.info("查询微信支付退款状态，退款单号: {}", queryRequest.getRefundNo());

        try {
            QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
            request.setOutRefundNo(queryRequest.getRefundNo());

            Refund wxRefund = refundService.queryByOutRefundNo(request);

            // 构建退款查询响应
            RefundQueryResponse response = buildRefundQueryResponse(wxRefund, queryRequest);

            log.info("微信支付退款状态查询成功，退款单号: {}, 状态: {}",
                    queryRequest.getRefundNo(), wxRefund.getStatus());

            return response;

        } catch (Exception e) {
            log.error("查询微信支付退款状态失败，退款单号: {}", queryRequest.getRefundNo(), e);
            throw PaymentException.refundQueryFailed(
                "查询微信支付退款状态失败: " + e.getMessage(),
                queryRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }

    @Override
    public boolean validateRefundCallback(Map<String, String> headers, String body) throws PaymentException {
        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            // 微信支付SDK会自动验证签名，这里我们尝试解析退款通知数据
            // 退款回调通知的数据结构与支付回调不同，需要使用合适的类来解析
            // 由于微信支付SDK可能没有专门的RefundNotification类，我们使用通用的解析方式
            String decryptedData = notificationParser.parse(requestParam, String.class);
            return decryptedData != null && !decryptedData.trim().isEmpty();

        } catch (Exception e) {
            log.error("验证微信支付退款回调失败", e);
            return false;
        }
    }

    @Override
    public RefundCallbackResult processRefundCallback(Map<String, String> headers, String body) throws PaymentException {
        log.info("处理微信支付退款回调, headers: {}, body: {}", headers, body);

        try {
            RequestParam requestParam = convertToRequestParam(headers, body);
            // 解析退款回调通知数据
            // 由于微信支付SDK的退款回调数据结构，我们需要手动解析JSON
            String decryptedData = notificationParser.parse(requestParam, String.class);

            // 这里应该解析JSON数据，提取退款信息
            // 为了简化实现，我们先返回一个基本的成功结果
            // 在实际项目中，需要使用JSON解析库（如Jackson或Gson）来解析decryptedData

            log.info("微信支付退款回调数据解密成功: {}", decryptedData);

            // TODO: 实际项目中需要解析JSON数据，提取以下字段：
            // - mchid: 商户号
            // - out_trade_no: 商户订单号
            // - transaction_id: 微信支付订单号
            // - out_refund_no: 商户退款单号
            // - refund_id: 微信退款单号
            // - refund_status: 退款状态
            // - success_time: 退款成功时间
            // - user_received_account: 退款入账账户
            // - amount: 金额信息

            // 临时实现：从body中提取基本信息（实际应该从解密后的数据中提取）
            RefundCallbackResult result = RefundCallbackResult.builder()
                    .success(true)
                    .build();

            log.info("微信支付退款回调处理成功");

            return result;

        } catch (Exception e) {
            log.error("处理微信支付退款回调失败", e);
            throw PaymentException.refundCallbackFailed(
                "处理微信支付退款回调失败: " + e.getMessage(),
                null,
                PaymentMethod.WECHAT_PAY,
                e
            );
        }
    }

    /**
     * 验证微信退款请求参数
     */
    private void validateWechatRefundRequest(RefundRequest refundRequest) throws PaymentException {
        if (refundRequest.getRefundNo() == null || refundRequest.getRefundNo().trim().isEmpty()) {
            throw PaymentException.validationError(
                "退款单号不能为空",
                refundRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }

        if (refundRequest.getRefundAmount() == null || refundRequest.getRefundAmount() <= 0) {
            throw PaymentException.validationError(
                "退款金额必须大于0",
                refundRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }

        if (refundRequest.getTotalAmount() == null || refundRequest.getTotalAmount() <= 0) {
            throw PaymentException.validationError(
                "原订单金额必须大于0",
                refundRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }

        if (refundRequest.getRefundAmount() > refundRequest.getTotalAmount()) {
            throw PaymentException.refundAmountError(
                "退款金额不能超过原订单金额",
                refundRequest.getOrderNo(),
                PaymentMethod.WECHAT_PAY
            );
        }
    }

    /**
     * 构建微信退款请求
     */
    private CreateRequest buildWechatRefundRequest(RefundRequest refundRequest) {
        CreateRequest createRequest = new CreateRequest();

        // 设置订单号（优先使用微信交易号，否则使用商户订单号）
        if (refundRequest.getTransactionId() != null && !refundRequest.getTransactionId().trim().isEmpty()) {
            createRequest.setTransactionId(refundRequest.getTransactionId());
        } else {
            createRequest.setOutTradeNo(refundRequest.getOrderNo());
        }

        createRequest.setOutRefundNo(refundRequest.getRefundNo());
        createRequest.setReason(refundRequest.getReason());

        // 设置退款回调URL
        if (refundRequest.getNotifyUrl() != null && !refundRequest.getNotifyUrl().trim().isEmpty()) {
            createRequest.setNotifyUrl(refundRequest.getNotifyUrl());
        } else if (refundNotifyUrl != null && !refundNotifyUrl.trim().isEmpty()) {
            createRequest.setNotifyUrl(refundNotifyUrl);
        }

        // 设置退款资金来源
        if (refundRequest.getFundsAccount() != null) {
            try {
                // 根据字符串值设置退款资金来源
                if ("AVAILABLE".equals(refundRequest.getFundsAccount())) {
                    createRequest.setFundsAccount(ReqFundsAccount.AVAILABLE);
                }  //                    createRequest.setFundsAccount(ReqFundsAccount.UNSETTLED);

            } catch (Exception e) {
                log.warn("设置退款资金来源失败: {}", refundRequest.getFundsAccount(), e);
            }
        }

        // 设置金额信息
        AmountReq amount = new AmountReq();
        amount.setRefund(Long.valueOf(refundRequest.getRefundAmount()));
        amount.setTotal(Long.valueOf(refundRequest.getTotalAmount()));
        amount.setCurrency("CNY");
        createRequest.setAmount(amount);

        // 设置商品详情（如果有）
        if (refundRequest.getGoodsDetail() != null && !refundRequest.getGoodsDetail().isEmpty()) {
            List<GoodsDetail> goodsDetails = refundRequest.getGoodsDetail().stream()
                    .map(this::convertToWechatGoodsDetail)
                    .collect(java.util.stream.Collectors.toList());
            createRequest.setGoodsDetail(goodsDetails);
        }

        return createRequest;
    }

    /**
     * 转换商品详情到微信格式
     */
    private GoodsDetail convertToWechatGoodsDetail(RefundRequest.RefundGoodsDetail goodsDetail) {
        GoodsDetail wechatGoodsDetail = new GoodsDetail();
        wechatGoodsDetail.setMerchantGoodsId(goodsDetail.getMerchantGoodsId());
        wechatGoodsDetail.setWechatpayGoodsId(goodsDetail.getWechatpayGoodsId());
        wechatGoodsDetail.setGoodsName(goodsDetail.getGoodsName());
        wechatGoodsDetail.setUnitPrice(goodsDetail.getUnitPrice() != null ?
            Long.valueOf(goodsDetail.getUnitPrice()) : null);
        wechatGoodsDetail.setRefundAmount(goodsDetail.getRefundAmount() != null ?
            Long.valueOf(goodsDetail.getRefundAmount()) : null);
        wechatGoodsDetail.setRefundQuantity(goodsDetail.getRefundQuantity());
        return wechatGoodsDetail;
    }

    /**
     * 构建退款响应
     */
    private RefundResponse buildRefundResponse(Refund wxRefund, RefundRequest refundRequest) {
        RefundResponse.RefundAmount amount = null;
        if (wxRefund.getAmount() != null) {
            amount = RefundResponse.RefundAmount.builder()
                    .total(wxRefund.getAmount().getTotal().intValue())
                    .refund(wxRefund.getAmount().getRefund().intValue())
                    .payerTotal(wxRefund.getAmount().getPayerTotal() != null ?
                            wxRefund.getAmount().getPayerTotal().intValue() : null)
                    .payerRefund(wxRefund.getAmount().getPayerRefund() != null ?
                            wxRefund.getAmount().getPayerRefund().intValue() : null)
                    .settlementRefund(wxRefund.getAmount().getSettlementRefund() != null ?
                            wxRefund.getAmount().getSettlementRefund().intValue() : null)
                    .settlementTotal(wxRefund.getAmount().getSettlementTotal() != null ?
                            wxRefund.getAmount().getSettlementTotal().intValue() : null)
                    .discountRefund(wxRefund.getAmount().getDiscountRefund() != null ?
                            wxRefund.getAmount().getDiscountRefund().intValue() : null)
                    .currency(wxRefund.getAmount().getCurrency())
                    .refundFee(wxRefund.getAmount().getRefundFee() != null ?
                            wxRefund.getAmount().getRefundFee().intValue() : null)
                    .build();
        }

        LocalDateTime successTime = null;
        if (wxRefund.getSuccessTime() != null) {
            successTime = parseWechatTime(wxRefund.getSuccessTime());
        }

        LocalDateTime createTime = null;
        if (wxRefund.getCreateTime() != null) {
            createTime = parseWechatTime(wxRefund.getCreateTime());
        }

        return RefundResponse.builder()
                .success(true)
                .orderNo(refundRequest.getOrderNo())
                .refundNo(wxRefund.getOutRefundNo())
                .refundId(wxRefund.getRefundId())
                .transactionId(wxRefund.getTransactionId())
                .refundStatus(wxRefund.getStatus().toString())
                .channel(wxRefund.getChannel() != null ? wxRefund.getChannel().toString() : null)
                .userReceivedAccount(wxRefund.getUserReceivedAccount())
                .successTime(successTime)
                .createTime(createTime)
                .amount(amount)
                .build();
    }

    /**
     * 构建退款查询响应
     */
    private RefundQueryResponse buildRefundQueryResponse(Refund wxRefund, RefundQueryRequest queryRequest) {
        RefundResponse.RefundAmount amount = null;
        if (wxRefund.getAmount() != null) {
            amount = RefundResponse.RefundAmount.builder()
                    .total(wxRefund.getAmount().getTotal().intValue())
                    .refund(wxRefund.getAmount().getRefund().intValue())
                    .payerTotal(wxRefund.getAmount().getPayerTotal() != null ?
                            wxRefund.getAmount().getPayerTotal().intValue() : null)
                    .payerRefund(wxRefund.getAmount().getPayerRefund() != null ?
                            wxRefund.getAmount().getPayerRefund().intValue() : null)
                    .settlementRefund(wxRefund.getAmount().getSettlementRefund() != null ?
                            wxRefund.getAmount().getSettlementRefund().intValue() : null)
                    .settlementTotal(wxRefund.getAmount().getSettlementTotal() != null ?
                            wxRefund.getAmount().getSettlementTotal().intValue() : null)
                    .discountRefund(wxRefund.getAmount().getDiscountRefund() != null ?
                            wxRefund.getAmount().getDiscountRefund().intValue() : null)
                    .currency(wxRefund.getAmount().getCurrency())
                    .refundFee(wxRefund.getAmount().getRefundFee() != null ?
                            wxRefund.getAmount().getRefundFee().intValue() : null)
                    .build();
        }

        LocalDateTime successTime = null;
        if (wxRefund.getSuccessTime() != null) {
            successTime = parseWechatTime(wxRefund.getSuccessTime());
        }

        LocalDateTime createTime = null;
        if (wxRefund.getCreateTime() != null) {
            createTime = parseWechatTime(wxRefund.getCreateTime());
        }

        return RefundQueryResponse.builder()
                .success(true)
                .orderNo(queryRequest.getOrderNo())
                .refundNo(wxRefund.getOutRefundNo())
                .refundId(wxRefund.getRefundId())
                .transactionId(wxRefund.getTransactionId())
                .refundStatus(wxRefund.getStatus().toString())
                .refundStatusDesc(getRefundStatusDescription(wxRefund.getStatus()))
                .channel(wxRefund.getChannel() != null ? wxRefund.getChannel().toString() : null)
                .userReceivedAccount(wxRefund.getUserReceivedAccount())
                .successTime(successTime)
                .createTime(createTime)
                .amount(amount)
                .build();
    }

    /**
     * 获取退款状态描述
     */
    private String getRefundStatusDescription(Refund.Status status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case SUCCESS:
                return "退款成功";
            case CLOSED:
                return "退款关闭";
            case PROCESSING:
                return "退款处理中";
            case ABNORMAL:
                return "退款异常";
            default:
                return "未知状态";
        }
    }

    /**
     * 用户信息服务接口
     */
//    public interface UserInfoService {
//        String getOpenIdByUserId(Long userId);
//    }
}
