package com.blsc.payment.dto;

import com.blsc.payment.model.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 退款请求数据传输对象
 * 
 * <AUTHOR>
 * @description 封装退款请求的所有必要信息
 * @date 2025/8/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundRequest {
    
    /**
     * 原订单号
     */
    private String orderNo;
    
    /**
     * 商户退款单号（唯一标识）
     */
    private String refundNo;
    
    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 退款金额（分）
     */
    private Integer refundAmount;
    
    /**
     * 原订单总金额（分）
     */
    private Integer totalAmount;
    
    /**
     * 退款原因
     */
    private String reason;
    
    /**
     * 退款回调通知URL
     */
    private String notifyUrl;
    
    /**
     * 退款资金来源
     * 可选值：AVAILABLE（可用余额）、UNSETTLED（未结算资金）
     */
    private String fundsAccount;
    
    /**
     * 退款商品详情
     */
    private List<RefundGoodsDetail> goodsDetail;
    
    /**
     * 退款商品详情
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundGoodsDetail {
        
        /**
         * 商户侧商品编码
         */
        private String merchantGoodsId;
        
        /**
         * 微信侧商品编码
         */
        private String wechatpayGoodsId;
        
        /**
         * 商品名称
         */
        private String goodsName;
        
        /**
         * 商品单价（分）
         */
        private Integer unitPrice;
        
        /**
         * 商品退款金额（分）
         */
        private Integer refundAmount;
        
        /**
         * 商品退货数量
         */
        private Integer refundQuantity;
    }
}
