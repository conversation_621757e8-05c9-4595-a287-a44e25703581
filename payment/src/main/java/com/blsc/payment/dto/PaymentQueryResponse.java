package com.blsc.payment.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 支付查询响应数据传输对象
 * 
 * <AUTHOR>
 * @description 封装支付状态查询响应的结果
 * @date 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentQueryResponse {
    
    /**
     * 订单号
     */
    private Long orderNo;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 支付状态
     */
    private String paymentStatus;
    
    /**
     * 支付状态描述
     */
    private String paymentStatusDesc;
    
    /**
     * 支付成功时间
     */
    private LocalDateTime successTime;
    
    /**
     * 支付金额（分）
     */
    private Integer totalAmount;
    
    /**
     * 是否查询成功
     */
    private boolean success;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建成功的查询响应
     * 
     * @param orderNo 订单号
     * @param transactionId 交易号
     * @param paymentStatus 支付状态
     * @param paymentStatusDesc 支付状态描述
     * @param successTime 成功时间
     * @return 查询响应对象
     */
    public static PaymentQueryResponse success(Long orderNo, String transactionId, 
                                             String paymentStatus, String paymentStatusDesc, 
                                             LocalDateTime successTime) {
        return PaymentQueryResponse.builder()
                .success(true)
                .orderNo(orderNo)
                .transactionId(transactionId)
                .paymentStatus(paymentStatus)
                .paymentStatusDesc(paymentStatusDesc)
                .successTime(successTime)
                .build();
    }
    
    /**
     * 创建失败的查询响应
     * 
     * @param orderNo 订单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 查询响应对象
     */
    public static PaymentQueryResponse failure(Long orderNo, String errorCode, String errorMessage) {
        return PaymentQueryResponse.builder()
                .success(false)
                .orderNo(orderNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
