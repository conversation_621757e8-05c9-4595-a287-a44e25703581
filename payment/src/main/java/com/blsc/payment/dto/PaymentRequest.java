package com.blsc.payment.dto;

import com.blsc.payment.model.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付请求数据传输对象
 * 
 * <AUTHOR>
 * @description 封装支付请求的所有必要信息
 * @date 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentRequest {
    
    /**
     * 订单号
     */
    private Long orderNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 支付金额（分）
     */
    private Integer amount;
    
    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;
    
    /**
     * 商品描述
     */
    private String productDescription;
    
    /**
     * 用户OpenID（支付渠道方的用户标识）
     */
    private String openId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 回调通知URL
     */
    private String notifyUrl;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 附加数据
     */
    private String attach;
    
    /**
     * 交易类型（如：JSAPI、NATIVE、APP等）
     */
    private String tradeType;
}
