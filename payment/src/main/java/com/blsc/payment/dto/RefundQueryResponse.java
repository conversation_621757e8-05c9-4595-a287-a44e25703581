package com.blsc.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 退款查询响应数据传输对象
 * 
 * <AUTHOR>
 * @description 封装退款状态查询响应的结果
 * @date 2025/8/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundQueryResponse {
    
    /**
     * 原订单号
     */
    private Long orderNo;
    
    /**
     * 商户退款单号
     */
    private String refundNo;
    
    /**
     * 第三方支付平台的退款单号
     */
    private String refundId;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 退款状态
     */
    private String refundStatus;
    
    /**
     * 退款状态描述
     */
    private String refundStatusDesc;
    
    /**
     * 退款渠道
     */
    private String channel;
    
    /**
     * 退款入账账户
     */
    private String userReceivedAccount;
    
    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;
    
    /**
     * 退款创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 退款金额信息
     */
    private RefundResponse.RefundAmount amount;
    
    /**
     * 是否查询成功
     */
    private boolean success;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建成功的退款查询响应
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param refundId 第三方退款单号
     * @param refundStatus 退款状态
     * @param refundStatusDesc 退款状态描述
     * @return 查询响应对象
     */
    public static RefundQueryResponse success(Long orderNo, String refundNo, String refundId, 
                                            String refundStatus, String refundStatusDesc) {
        return RefundQueryResponse.builder()
                .success(true)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .refundId(refundId)
                .refundStatus(refundStatus)
                .refundStatusDesc(refundStatusDesc)
                .build();
    }
    
    /**
     * 创建失败的退款查询响应
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 查询响应对象
     */
    public static RefundQueryResponse failure(Long orderNo, String refundNo, String errorCode, String errorMessage) {
        return RefundQueryResponse.builder()
                .success(false)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
