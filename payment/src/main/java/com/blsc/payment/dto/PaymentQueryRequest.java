package com.blsc.payment.dto;

import com.blsc.payment.model.PaymentMethod;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;


/**
 * 支付查询请求数据传输对象
 * 
 * <AUTHOR>
 * @description 封装支付状态查询请求的参数
 * @date 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentQueryRequest {
    
    /**
     * 订单号
     */
    private Long orderNo;
    
    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 商户号
     */
    private String merchantId;
}
