package com.blsc.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 退款响应数据传输对象
 * 
 * <AUTHOR>
 * @description 封装退款响应的所有信息
 * @date 2025/8/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundResponse {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 原订单号
     */
    private Long orderNo;
    
    /**
     * 商户退款单号
     */
    private String refundNo;
    
    /**
     * 第三方支付平台的退款单号
     */
    private String refundId;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 退款状态
     */
    private String refundStatus;
    
    /**
     * 退款渠道
     */
    private String channel;
    
    /**
     * 退款入账账户
     */
    private String userReceivedAccount;
    
    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;
    
    /**
     * 退款创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 退款金额信息
     */
    private RefundAmount amount;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 退款金额信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundAmount {
        
        /**
         * 订单总金额（分）
         */
        private Integer total;
        
        /**
         * 退款金额（分）
         */
        private Integer refund;
        
        /**
         * 用户实际支付金额（分）
         */
        private Integer payerTotal;
        
        /**
         * 用户退款金额（分）
         */
        private Integer payerRefund;
        
        /**
         * 应结退款金额（分）
         */
        private Integer settlementRefund;
        
        /**
         * 应结订单金额（分）
         */
        private Integer settlementTotal;
        
        /**
         * 优惠退款金额（分）
         */
        private Integer discountRefund;
        
        /**
         * 退款币种
         */
        private String currency;
        
        /**
         * 手续费退款金额（分）
         */
        private Integer refundFee;
    }
    
    /**
     * 创建成功的退款响应
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param refundId 第三方退款单号
     * @param refundStatus 退款状态
     * @return 退款响应对象
     */
    public static RefundResponse success(Long orderNo, String refundNo, String refundId, String refundStatus) {
        return RefundResponse.builder()
                .success(true)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .refundId(refundId)
                .refundStatus(refundStatus)
                .build();
    }
    
    /**
     * 创建失败的退款响应
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 退款响应对象
     */
    public static RefundResponse failure(Long orderNo, String refundNo, String errorCode, String errorMessage) {
        return RefundResponse.builder()
                .success(false)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
