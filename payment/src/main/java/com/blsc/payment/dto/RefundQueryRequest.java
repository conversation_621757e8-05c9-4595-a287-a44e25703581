package com.blsc.payment.dto;

import com.blsc.payment.model.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退款查询请求数据传输对象
 * 
 * <AUTHOR>
 * @description 封装退款状态查询请求的参数
 * @date 2025/8/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundQueryRequest {
    
    /**
     * 原订单号
     */
    private Long orderNo;
    
    /**
     * 商户退款单号
     */
    private String refundNo;
    
    /**
     * 支付方式
     */
    private PaymentMethod paymentMethod;
    
    /**
     * 第三方支付平台的退款单号
     */
    private String refundId;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 商户号
     */
    private String merchantId;
}
