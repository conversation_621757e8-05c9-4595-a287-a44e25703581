package com.blsc.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付响应数据传输对象
 * 
 * <AUTHOR>
 * @description 封装支付响应的所有信息
 * @date 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentResponse {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 订单号
     */
    private Long orderNo;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 支付状态
     */
    private String paymentStatus;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    
    /**
     * 客户端调用支付所需的参数
     * 不同支付方式返回的参数不同
     */
    private Map<String, Object> paymentParams;
    
    /**
     * 支付跳转URL（适用于网页支付）
     */
    private String paymentUrl;
    
    /**
     * 二维码内容（适用于扫码支付）
     */
    private String qrCode;
    
    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 创建成功的支付响应
     * 
     * @param orderNo 订单号
     * @param paymentParams 支付参数
     * @return 支付响应对象
     */
    public static PaymentResponse success(Long orderNo, Map<String, Object> paymentParams) {
        return PaymentResponse.builder()
                .success(true)
                .orderNo(orderNo)
                .paymentParams(paymentParams)
                .build();
    }
    
    /**
     * 创建失败的支付响应
     * 
     * @param orderNo 订单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 支付响应对象
     */
    public static PaymentResponse failure(Long orderNo, String errorCode, String errorMessage) {
        return PaymentResponse.builder()
                .success(false)
                .orderNo(orderNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
