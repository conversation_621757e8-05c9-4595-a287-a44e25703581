package com.blsc.payment.example;

import com.blsc.payment.PaymentModule;
import com.blsc.payment.config.PaymentConfig;
import com.blsc.payment.dto.*;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.model.RefundCallbackResult;
import com.blsc.payment.strategy.PaymentException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款功能使用示例
 * 
 * <AUTHOR>
 * @description 演示如何使用payment模块的退款功能
 * @date 2025/8/18
 */
@Slf4j
public class RefundExample {
    
    private PaymentModule paymentModule;
    
    public RefundExample() {
        // 初始化支付模块
        PaymentConfig config = new PaymentConfig();
        // 配置微信支付参数...
        this.paymentModule = new PaymentModule(config);
    }
    
    /**
     * 申请退款示例
     */
    public void refundExample() {
        try {
            // 构建退款请求
            RefundRequest refundRequest = RefundRequest.builder()
                    .orderNo(1234567890L)
                    .refundNo("REFUND_" + System.currentTimeMillis())
                    .paymentMethod(PaymentMethod.WECHAT_PAY)
                    .refundAmount(1000) // 退款10元（分）
                    .totalAmount(2000)  // 原订单20元（分）
                    .reason("用户申请退款")
                    .build();
            
            // 申请退款
            RefundResponse response = paymentModule.refund(refundRequest);
            
            if (response.isSuccess()) {
                log.info("退款申请成功: 订单号={}, 退款单号={}, 微信退款单号={}, 状态={}", 
                        response.getOrderNo(), response.getRefundNo(), 
                        response.getRefundId(), response.getRefundStatus());
            } else {
                log.error("退款申请失败: {}", response.getErrorMessage());
            }
            
        } catch (PaymentException e) {
            log.error("退款申请异常: {}", e.getFullErrorMessage(), e);
        }
    }
    
    /**
     * 查询退款状态示例
     */
    public void queryRefundExample() {
        try {
            // 构建退款查询请求
            RefundQueryRequest queryRequest = RefundQueryRequest.builder()
                    .orderNo(1234567890L)
                    .refundNo("REFUND_1234567890")
                    .paymentMethod(PaymentMethod.WECHAT_PAY)
                    .build();
            
            // 查询退款状态
            RefundQueryResponse response = paymentModule.queryRefund(queryRequest);
            
            if (response.isSuccess()) {
                log.info("退款查询成功: 退款单号={}, 状态={}, 描述={}", 
                        response.getRefundNo(), response.getRefundStatus(), 
                        response.getRefundStatusDesc());
                
                if (response.getAmount() != null) {
                    log.info("退款金额信息: 总金额={}分, 退款金额={}分", 
                            response.getAmount().getTotal(), response.getAmount().getRefund());
                }
            } else {
                log.error("退款查询失败: {}", response.getErrorMessage());
            }
            
        } catch (PaymentException e) {
            log.error("退款查询异常: {}", e.getFullErrorMessage(), e);
        }
    }
    
    /**
     * 处理退款回调示例
     */
    public void processRefundCallbackExample() {
        try {
            // 模拟微信支付退款回调的请求头和请求体
            Map<String, String> headers = new HashMap<>();
            headers.put("Wechatpay-Serial", "certificate_serial_number");
            headers.put("Wechatpay-Nonce", "random_nonce");
            headers.put("Wechatpay-Timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            headers.put("Wechatpay-Signature", "signature_value");
            
            String body = "{\"id\":\"notification_id\",\"create_time\":\"2025-08-18T10:00:00+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"REFUND.SUCCESS\",\"summary\":\"退款成功\",\"resource\":{\"algorithm\":\"AEAD_AES_256_GCM\",\"original_type\":\"refund\",\"ciphertext\":\"encrypted_data\",\"nonce\":\"nonce_value\",\"associated_data\":\"\"}}";
            
            // 验证退款回调
            boolean isValid = paymentModule.validateRefundCallback(PaymentMethod.WECHAT_PAY, headers, body);
            
            if (isValid) {
                log.info("退款回调验证成功");
                
                // 处理退款回调
                RefundCallbackResult result = paymentModule.processRefundCallback(PaymentMethod.WECHAT_PAY, headers, body);
                
                if (result.isSuccess()) {
                    log.info("退款回调处理成功: 订单号={}, 退款单号={}, 退款状态={}", 
                            result.getOrderNo(), result.getRefundNo(), result.getRefundStatus());
                    
                    // 在这里更新业务系统中的退款状态
                    updateRefundStatusInBusiness(result);
                } else {
                    log.error("退款回调处理失败: {}", result.getErrorMessage());
                }
            } else {
                log.error("退款回调验证失败");
            }
            
        } catch (PaymentException e) {
            log.error("退款回调处理异常: {}", e.getFullErrorMessage(), e);
        }
    }
    
    /**
     * 更新业务系统中的退款状态
     */
    private void updateRefundStatusInBusiness(RefundCallbackResult result) {
        // TODO: 实现业务逻辑
        // 1. 根据订单号和退款单号更新数据库中的退款状态
        // 2. 发送退款成功通知给用户
        // 3. 记录退款日志
        // 4. 其他业务处理
        
        log.info("业务系统退款状态更新完成: 订单号={}, 退款单号={}", 
                result.getOrderNo(), result.getRefundNo());
    }
    
    public static void main(String[] args) {
        RefundExample example = new RefundExample();
        
        // 演示退款功能
        log.info("=== 退款申请示例 ===");
        example.refundExample();
        
        log.info("=== 退款查询示例 ===");
        example.queryRefundExample();
        
        log.info("=== 退款回调处理示例 ===");
        example.processRefundCallbackExample();
    }
}
