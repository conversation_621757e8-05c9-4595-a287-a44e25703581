package com.blsc.payment.factory;

import com.blsc.payment.config.PaymentConfig;
import com.blsc.payment.model.PaymentErrorType;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.strategy.PaymentException;
import com.blsc.payment.strategy.PaymentStrategy;
import com.blsc.payment.strategy.impl.WechatPaymentStrategy;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支付策略工厂类
 *
 * <AUTHOR>
 * @description 管理支付策略实例的工厂类，不依赖Spring框架
 * @date 2025-07-29
 */
@Slf4j
public class PaymentStrategyFactory {

    private final Map<PaymentMethod, PaymentStrategy> strategyMap = new ConcurrentHashMap<>();
    private final PaymentConfig paymentConfig;

    public PaymentStrategyFactory(PaymentConfig paymentConfig) {
        this.paymentConfig = paymentConfig;
        initializeStrategies();
    }

    /**
     * 初始化所有支付策略
     */
    private void initializeStrategies() {
        try {
            // 初始化微信支付策略
            if (paymentConfig.getWechatPay() != null) {
                WechatPaymentStrategy wechatStrategy = createWechatPaymentStrategy();
                if (wechatStrategy != null && wechatStrategy.isAvailable()) {
                    strategyMap.put(PaymentMethod.WECHAT_PAY, wechatStrategy);
                    log.info("微信支付策略初始化成功");
                } else {
                    log.warn("微信支付策略配置不完整，跳过初始化");
                }
            }

            // 可以在这里添加其他支付策略的初始化
            // 例如：支付宝、银联等

            log.info("支付策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());

        } catch (Exception e) {
            log.error("支付策略工厂初始化失败", e);
            throw new PaymentException(PaymentErrorType.SYSTEM_ERROR.getCode(), "支付策略工厂初始化失败", e);
        }
    }

    /**
     * 创建微信支付策略实例
     */
    private WechatPaymentStrategy createWechatPaymentStrategy() {
        PaymentConfig.WechatPayConfig wechatConfig = paymentConfig.getWechatPay();

        RSAAutoCertificateConfig rsaConfig;
        try {
            if (wechatConfig.getPrivateKeyPath() != null && !wechatConfig.getPrivateKeyPath().trim().isEmpty()) {
                // 使用证书配置
                rsaConfig = new RSAAutoCertificateConfig.Builder()
                        .merchantId(wechatConfig.getMerchantId())
                        .privateKeyFromPath(wechatConfig.getPrivateKeyPath())
                        .merchantSerialNumber(wechatConfig.getSerialNumber())
                        .apiV3Key(wechatConfig.getApiKey())
                        .build();
            } else {
                log.warn("微信支付证书配置不完整");
                throw PaymentException.systemError("微信支付证书配置不完整", null);
            }
        } catch (Exception e) {
            log.error("创建微信支付配置失败", e);
            return null;
        }

        return new WechatPaymentStrategy(
                wechatConfig.getAppId(),
                wechatConfig.getMerchantId(),
                wechatConfig.getPayNotifyUrl(),
                wechatConfig.getRefundNotifyUrl(),
                new JsapiServiceExtension.Builder().config(rsaConfig).build(), // jsapiServiceExtension - 需要外部提供
                new NotificationParser(rsaConfig) // notificationParser - 需要外部提供
        );
    }

    /**
     * 根据支付方式获取对应的支付策略
     *
     * @param paymentMethod 支付方式
     * @return 支付策略实例
     * @throws IllegalArgumentException 如果不支持该支付方式
     */
    public PaymentStrategy getStrategy(PaymentMethod paymentMethod) {
        PaymentStrategy strategy = strategyMap.get(paymentMethod);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付方式: " + paymentMethod);
        }

        if (!strategy.isAvailable()) {
            throw new IllegalStateException("支付方式不可用: " + paymentMethod);
        }

        return strategy;
    }

    /**
     * 获取所有可用的支付方式
     *
     * @return 可用的支付方式列表
     */
    public List<PaymentMethod> getAvailablePaymentMethods() {
        List<PaymentMethod> availableMethods = new ArrayList<>();

        for (Map.Entry<PaymentMethod, PaymentStrategy> entry : strategyMap.entrySet()) {
            if (entry.getValue().isAvailable()) {
                availableMethods.add(entry.getKey());
            }
        }

        // 按优先级排序
        availableMethods.sort(Comparator.comparingInt(method ->
                strategyMap.get(method).getPriority()));

        return availableMethods;
    }

    /**
     * 检查指定支付方式是否可用
     *
     * @param paymentMethod 支付方式
     * @return 是否可用
     */
    public boolean isAvailable(PaymentMethod paymentMethod) {
        PaymentStrategy strategy = strategyMap.get(paymentMethod);
        return strategy != null && strategy.isAvailable();
    }

    /**
     * 获取所有已注册的支付策略
     *
     * @return 支付策略映射
     */
    public Map<PaymentMethod, PaymentStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }
}
