package com.blsc.payment.config;

import lombok.Data;

/**
 * 支付配置类
 * 
 * <AUTHOR>
 * @description 支付模块的配置参数管理
 * @date 2025-07-29
 */
@Data
public class PaymentConfig {
    
    /**
     * 微信支付配置
     */
    private WechatPayConfig wechatPay;
    
    /**
     * 支付宝配置
     */
    private AlipayConfig alipay;
    
    @Data
    public static class WechatPayConfig {
        /**
         * 微信应用ID
         */
        private String appId;
        
        /**
         * 商户号
         */
        private String merchantId;
        
        /**
         * 支付回调通知URL
         */
        private String payNotifyUrl;
        
        /**
         * 退款回调通知URL
         */
        private String refundNotifyUrl;
        
        /**
         * API密钥
         */
        private String apiKey;
        
        /**
         * 证书路径
         */
        private String certPath;
        
        /**
         * 私钥路径
         */
        private String privateKeyPath;
        
        /**
         * 证书序列号
         */
        private String serialNumber;
    }
    
    @Data
    public static class AlipayConfig {
        /**
         * 支付宝应用ID
         */
        private String appId;
        
        /**
         * 支付宝网关
         */
        private String gatewayUrl;
        
        /**
         * 应用私钥
         */
        private String privateKey;
        
        /**
         * 支付宝公钥
         */
        private String alipayPublicKey;
        
        /**
         * 支付回调通知URL
         */
        private String notifyUrl;
        
        /**
         * 支付成功跳转URL
         */
        private String returnUrl;
    }
}
