package com.blsc.payment.model;

/**
 * 支付方式枚举
 * 
 * <AUTHOR>
 * @description 定义系统支持的支付方式
 * @date 2025/7/29
 */
public enum PaymentMethod {
    
    /**
     * 微信支付
     */
    WECHAT_PAY("WECHAT_PAY", "微信支付"),
    
    /**
     * 支付宝支付
     */
    ALIPAY("ALIPAY", "支付宝支付"),
    
    /**
     * 银联支付
     */
    UNION_PAY("UNION_PAY", "银联支付");
    
    private final String code;
    private final String description;
    
    PaymentMethod(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取支付方式代码
     * 
     * @return 支付方式代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取支付方式描述
     * 
     * @return 支付方式描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取支付方式
     * 
     * @param code 支付方式代码
     * @return 支付方式枚举，如果未找到则返回null
     */
    public static PaymentMethod fromCode(String code) {
        for (PaymentMethod method : values()) {
            if (method.code.equals(code)) {
                return method;
            }
        }
        return null;
    }
}
