package com.blsc.payment.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 退款回调处理结果
 * 
 * <AUTHOR>
 * @description 封装退款回调处理的结果信息
 * @date 2025/8/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundCallbackResult {
    
    /**
     * 处理是否成功
     */
    private boolean success;
    
    /**
     * 原订单号
     */
    private Long orderNo;
    
    /**
     * 商户退款单号
     */
    private String refundNo;
    
    /**
     * 第三方支付平台的退款单号
     */
    private String refundId;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 退款状态
     */
    private String refundStatus;
    
    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;
    
    /**
     * 退款入账账户
     */
    private String userReceivedAccount;
    
    /**
     * 退款金额信息
     */
    private RefundAmount amount;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 退款金额信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundAmount {
        
        /**
         * 原订单金额（分）
         */
        private Integer total;
        
        /**
         * 退款金额（分）
         */
        private Integer refund;
        
        /**
         * 用户实际支付金额（分）
         */
        private Integer payerTotal;
        
        /**
         * 用户退款金额（分）
         */
        private Integer payerRefund;
    }
    
    /**
     * 创建成功的退款回调结果
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param refundId 第三方退款单号
     * @param transactionId 交易号
     * @param refundStatus 退款状态
     * @param successTime 退款成功时间
     * @return 回调结果对象
     */
    public static RefundCallbackResult success(Long orderNo, String refundNo, String refundId, 
                                             String transactionId, String refundStatus, 
                                             LocalDateTime successTime) {
        return RefundCallbackResult.builder()
                .success(true)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .refundId(refundId)
                .transactionId(transactionId)
                .refundStatus(refundStatus)
                .successTime(successTime)
                .build();
    }
    
    /**
     * 创建失败的退款回调结果
     * 
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 回调结果对象
     */
    public static RefundCallbackResult failure(Long orderNo, String refundNo, String errorCode, String errorMessage) {
        return RefundCallbackResult.builder()
                .success(false)
                .orderNo(orderNo)
                .refundNo(refundNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
