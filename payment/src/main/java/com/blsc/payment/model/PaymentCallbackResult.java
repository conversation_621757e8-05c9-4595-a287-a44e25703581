package com.blsc.payment.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 支付回调处理结果
 * 
 * <AUTHOR>
 * @description 封装支付回调处理的结果信息
 * @date 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentCallbackResult {
    
    /**
     * 处理是否成功
     */
    private boolean success;
    
    /**
     * 订单号
     */
    private Long orderNo;
    
    /**
     * 第三方支付平台的交易号
     */
    private String transactionId;
    
    /**
     * 支付状态
     */
    private String paymentStatus;
    
    /**
     * 支付成功时间
     */
    private LocalDateTime paymentTime;
    
    /**
     * 支付金额（分）
     */
    private Integer totalAmount;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 创建成功的回调结果
     * 
     * @param orderNo 订单号
     * @param transactionId 交易号
     * @param paymentStatus 支付状态
     * @param paymentTime 支付时间
     * @param totalAmount 支付金额
     * @return 回调结果对象
     */
    public static PaymentCallbackResult success(Long orderNo, String transactionId, 
                                              String paymentStatus, LocalDateTime paymentTime, 
                                              Integer totalAmount) {
        return PaymentCallbackResult.builder()
                .success(true)
                .orderNo(orderNo)
                .transactionId(transactionId)
                .paymentStatus(paymentStatus)
                .paymentTime(paymentTime)
                .totalAmount(totalAmount)
                .build();
    }
    
    /**
     * 创建失败的回调结果
     * 
     * @param orderNo 订单号
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @return 回调结果对象
     */
    public static PaymentCallbackResult failure(Long orderNo, String errorCode, String errorMessage) {
        return PaymentCallbackResult.builder()
                .success(false)
                .orderNo(orderNo)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
