package com.blsc.payment.model;

/**
 * 支付错误类型枚举
 *
 * <AUTHOR>
 * @description 定义支付过程中的主要错误类型
 * @date 2025/7/29
 */
public enum PaymentErrorType {

    /**
     * 参数验证错误
     */
    VALIDATION_ERROR("VALIDATION_ERROR", "参数验证错误"),

    /**
     * 支付失败
     */
    PAYMENT_FAILED("PAYMENT_FAILED", "支付失败"),

    /**
     * 支付超时
     */
    PAYMENT_TIMEOUT("PAYMENT_TIMEOUT", "支付超时"),

    /**
     * 回调处理失败
     */
    CALLBACK_FAILED("CALLBACK_FAILED", "回调处理失败"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统错误"),

    /**
     * 退款失败
     */
    REFUND_FAILED("REFUND_FAILED", "退款失败"),

    /**
     * 退款查询失败
     */
    REFUND_QUERY_FAILED("REFUND_QUERY_FAILED", "退款查询失败"),

    /**
     * 退款回调处理失败
     */
    REFUND_CALLBACK_FAILED("REFUND_CALLBACK_FAILED", "退款回调处理失败"),

    /**
     * 退款金额错误
     */
    REFUND_AMOUNT_ERROR("REFUND_AMOUNT_ERROR", "退款金额错误"),

    /**
     * 退款单号重复
     */
    REFUND_NO_DUPLICATE("REFUND_NO_DUPLICATE", "退款单号重复");

    private final String code;
    private final String description;

    PaymentErrorType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取错误类型代码
     *
     * @return 错误类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取错误类型描述
     *
     * @return 错误类型描述
     */
    public String getDescription() {
        return description;
    }
}
