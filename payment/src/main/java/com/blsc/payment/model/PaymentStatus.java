package com.blsc.payment.model;

/**
 * 支付状态枚举
 * 
 * <AUTHOR>
 * @description 统一的支付状态定义，适用于所有支付渠道
 * @date 2025-07-29
 */
public enum PaymentStatus {

    /**
     * 待支付
     */
    PENDING("PENDING", "待支付"),

    /**
     * 支付中
     */
    PROCESSING("PROCESSING", "支付中"),

    /**
     * 支付成功
     */
    SUCCESS("SUCCESS", "支付成功"),

    /**
     * 支付失败
     */
    FAILED("FAILED", "支付失败"),

    /**
     * 支付取消
     */
    CANCELLED("CANCELLED", "支付取消"),

    /**
     * 支付超时
     */
    TIMEOUT("TIMEOUT", "支付超时"),

    /**
     * 退款中
     */
    REFUNDING("REFUNDING", "退款中"),

    /**
     * 已退款
     */
    REFUNDED("REFUNDED", "已退款"),

    /**
     * 部分退款
     */
    PARTIAL_REFUNDED("PARTIAL_REFUNDED", "部分退款"),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态");

    private final String code;
    private final String description;

    PaymentStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取支付状态
     */
    public static PaymentStatus fromCode(String code) {
        for (PaymentStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("不支持的支付状态: " + code);
    }

    /**
     * 判断是否为最终状态（不可再变更）
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == CANCELLED || 
               this == TIMEOUT || this == REFUNDED;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailureStatus() {
        return this == FAILED || this == CANCELLED || this == TIMEOUT;
    }
}
