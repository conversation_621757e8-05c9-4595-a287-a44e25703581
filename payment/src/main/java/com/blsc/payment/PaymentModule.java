package com.blsc.payment;

import com.blsc.payment.config.PaymentConfig;
import com.blsc.payment.dto.PaymentQueryRequest;
import com.blsc.payment.dto.PaymentQueryResponse;
import com.blsc.payment.dto.PaymentRequest;
import com.blsc.payment.dto.PaymentResponse;
import com.blsc.payment.factory.PaymentStrategyFactory;
import com.blsc.payment.model.PaymentCallbackResult;
import com.blsc.payment.model.PaymentMethod;
import com.blsc.payment.strategy.PaymentException;
import com.blsc.payment.strategy.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 支付模块主入口类
 * 
 * <AUTHOR>
 * @description 支付模块的主要入口，提供模块初始化和服务获取功能
 * @date 2025-07-29
 */
@Slf4j
public class PaymentModule {
    
    private final PaymentConfig paymentConfig;
    private final PaymentStrategyFactory strategyFactory;

    /**
     * 构造函数
     *
     * @param paymentConfig 支付配置
     */
    public PaymentModule(PaymentConfig paymentConfig) {
        this.paymentConfig = paymentConfig;
        this.strategyFactory = new PaymentStrategyFactory(paymentConfig);

        log.info("支付模块初始化完成");
    }

    /**
     * 创建支付订单
     *
     * @param paymentRequest 支付请求参数
     * @return 支付响应结果
     * @throws PaymentException 支付异常
     */
    public PaymentResponse createPayment(PaymentRequest paymentRequest) throws PaymentException {
        PaymentStrategy strategy = strategyFactory.getStrategy(paymentRequest.getPaymentMethod());
        return strategy.createPayment(paymentRequest);
    }

    /**
     * 查询支付状态
     *
     * @param queryRequest 查询请求参数
     * @return 查询响应结果
     * @throws PaymentException 支付异常
     */
    public PaymentQueryResponse queryPayment(PaymentQueryRequest queryRequest) throws PaymentException {
        PaymentStrategy strategy = strategyFactory.getStrategy(queryRequest.getPaymentMethod());
        return strategy.queryPayment(queryRequest);
    }

    /**
     * 验证支付回调
     *
     * @param paymentMethod 支付方式
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 是否验证成功
     * @throws PaymentException 支付异常
     */
    public boolean validateCallback(PaymentMethod paymentMethod,
                                   Map<String, String> headers,
                                   String body) throws PaymentException {
        PaymentStrategy strategy = strategyFactory.getStrategy(paymentMethod);
        return strategy.validateCallback(headers, body);
    }

    /**
     * 处理支付回调
     *
     * @param paymentMethod 支付方式
     * @param headers 回调请求头
     * @param body 回调请求体
     * @return 回调处理结果
     * @throws PaymentException 支付异常
     */
    public PaymentCallbackResult processCallback(PaymentMethod paymentMethod,
                                                Map<String, String> headers,
                                                String body) throws PaymentException {
        PaymentStrategy strategy = strategyFactory.getStrategy(paymentMethod);
        return strategy.processCallback(headers, body);
    }

    /**
     * 获取所有可用的支付方式
     *
     * @return 可用的支付方式列表
     */
    public List<PaymentMethod> getAvailablePaymentMethods() {
        return strategyFactory.getAvailablePaymentMethods();
    }

    /**
     * 检查指定支付方式是否可用
     *
     * @param paymentMethod 支付方式
     * @return 是否可用
     */
    public boolean isPaymentMethodAvailable(PaymentMethod paymentMethod) {
        return strategyFactory.isAvailable(paymentMethod);
    }
    
    /**
     * 获取支付策略工厂
     * 
     * @return 支付策略工厂
     */
    public PaymentStrategyFactory getStrategyFactory() {
        return strategyFactory;
    }
    
    /**
     * 获取支付配置
     * 
     * @return 支付配置
     */
    public PaymentConfig getPaymentConfig() {
        return paymentConfig;
    }
    
}
