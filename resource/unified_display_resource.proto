syntax = "proto3";

package com.blsc.article.grpc.resource;

option java_multiple_files = true;
option java_package = "com.blsc.article.grpc.resource";
option java_outer_classname = "UnifiedDisplayResourceProto";

import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

// 统一展示资源服务
service UnifiedDisplayResourceService {
  // 标准CRUD操作
  rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse);
  rpc GetResource(GetResourceRequest) returns (GetResourceResponse);
  rpc CreateResource(CreateResourceRequest) returns (CreateResourceResponse);
  rpc UpdateResource(UpdateResourceRequest) returns (UpdateResourceResponse);
  rpc DeleteResource(DeleteResourceRequest) returns (DeleteResourceResponse);

  // 搜索操作
  rpc SearchResources(SearchResourcesRequest) returns (SearchResourcesResponse);

  // 批量操作
  rpc BatchGetResources(BatchGetResourcesRequest) returns (BatchGetResourcesResponse);
  rpc BatchUpdateResources(BatchUpdateResourcesRequest) returns (BatchUpdateResourcesResponse);
  rpc BatchDeleteResources(BatchDeleteResourcesRequest) returns (BatchDeleteResourcesResponse);

  // 自定义业务操作
  rpc PublishResource(PublishResourceRequest) returns (PublishResourceResponse);
  rpc UnpublishResource(UnpublishResourceRequest) returns (UnpublishResourceResponse);
  rpc FeatureResource(FeatureResourceRequest) returns (FeatureResourceResponse);
  rpc UnfeatureResource(UnfeatureResourceRequest) returns (UnfeatureResourceResponse);
  rpc PinResource(PinResourceRequest) returns (PinResourceResponse);
  rpc UnpinResource(UnpinResourceRequest) returns (UnpinResourceResponse);
  rpc IncreaseViewCount(IncreaseViewCountRequest) returns (IncreaseViewCountResponse);

  // 分类相关操作
  rpc ListCategories(ListCategoriesRequest) returns (ListCategoriesResponse);
  rpc GetCategory(GetCategoryRequest) returns (GetCategoryResponse);
  rpc GetCategoryTree(GetCategoryTreeRequest) returns (GetCategoryTreeResponse);
  rpc ListResourcesByCategory(ListResourcesByCategoryRequest) returns (ListResourcesByCategoryResponse);
  rpc ListFeaturedResourcesByCategory(ListFeaturedResourcesByCategoryRequest) returns (ListFeaturedResourcesByCategoryResponse);
  rpc ListPopularResourcesByCategory(ListPopularResourcesByCategoryRequest) returns (ListPopularResourcesByCategoryResponse);
}

// 资源信息
message Resource {
  int64 id = 1;                                       // 资源ID
  string resource_type_code = 2;                           // 资源类型编码
  string resource_type_name = 3;                      // 资源类型名称
  string display_position = 4;                        // 显示位置
  string display_position_desc = 5;                   // 显示位置描述
  string title = 6;                                   // 资源标题
  string subtitle = 7;                                // 副标题/描述
  string content = 8;                                 // 资源内容
  string cover_image_url = 9;                         // 封面图片URL
  string cover_image_name = 10;                       // 封面图片文件名
  string target_url = 11;                             // 跳转链接URL
  int32 target_type = 12;                             // 跳转类型：1-网页 2-弹窗 3-小程序 4-内部跳转
  string app_id = 13;                                 // 小程序AppId
  string author = 14;                                 // 作者/发布人
  int32 sort_order = 15;                              // 排序值
  int32 status = 16;                                  // 状态：1-草稿 2-已发布 3-已下架
  google.protobuf.BoolValue is_display = 17;                               // 是否显示
  google.protobuf.BoolValue is_featured = 18;                              // 是否推荐
  google.protobuf.BoolValue is_top = 19;                                   // 是否置顶
  google.protobuf.Timestamp publish_time = 20;       // 发布时间
  google.protobuf.Timestamp expire_time = 21;        // 过期时间
  int32 view_count = 22;                              // 浏览次数
  map<string, google.protobuf.Value> attributes = 23;               // 扩展属性
  int64 category_id = 24;                             // 分类ID
  string category_name = 25;                          // 分类名称
  string category_code = 26;                          // 分类编码
  int64 parent_category_id = 27;                      // 父分类ID
  string category_path = 28;                          // 分类路径
}



// ========== 标准CRUD请求和响应 ==========

// 列出资源请求
message ListResourcesRequest {
  string resource_type_code = 1;                           // 资源类型编码
  string display_position = 2;                        // 显示位置
  string title = 3;                                   // 资源标题（模糊查询）
  string author = 4;                                  // 作者/发布人
  int32 status = 5;                                   // 状态
  google.protobuf.BoolValue is_display = 6;                                // 是否显示
  google.protobuf.BoolValue is_featured = 7;                               // 是否推荐
  google.protobuf.BoolValue is_top = 8;                                    // 是否置顶
  google.protobuf.Timestamp publish_time_start = 9;  // 发布时间开始
  google.protobuf.Timestamp publish_time_end = 10;   // 发布时间结束
  int32 page_num = 11;                                // 页码
  int32 page_size = 12;                               // 每页大小
  string sort_field = 13;                             // 排序字段
  string sort_type = 14;                              // 排序方式
  string keyword = 15;                                // 关键词搜索
  string category_code = 16;                          // 分类编码
}

// 列出资源响应
message ListResourcesResponse {
  repeated Resource resources = 1;                    // 资源列表
  int32 page_num = 2;                                 // 当前页码
  int32 page_size = 3;                                // 每页大小
  int64 total = 4;                                    // 总记录数
  int32 pages = 5;                                    // 总页数
}

// 获取资源请求
message GetResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message GetResourceResponse {
  Resource resource = 1;                              // 资源信息
}

// 创建资源请求
message CreateResourceRequest {
  string resource_type_code = 1;                           // 资源类型编码
  string display_position = 2;                        // 显示位置
  string title = 3;                                   // 资源标题
  string subtitle = 4;                                // 副标题/描述
  string content = 5;                                 // 资源内容
  string cover_image_url = 6;                         // 封面图片URL
  string cover_image_name = 7;                        // 封面图片文件名
  string target_url = 8;                              // 跳转链接URL
  int32 target_type = 9;                              // 跳转类型
  string app_id = 10;                                 // 小程序AppId
  string author = 11;                                 // 作者/发布人
  int32 sort_order = 12;                              // 排序值
  int32 status = 13;                                  // 状态
  google.protobuf.BoolValue is_display = 14;                               // 是否显示
  google.protobuf.BoolValue is_featured = 15;                              // 是否推荐
  google.protobuf.BoolValue is_top = 16;                                   // 是否置顶
  google.protobuf.Timestamp publish_time = 17;       // 发布时间
  google.protobuf.Timestamp expire_time = 18;        // 过期时间
  map<string, google.protobuf.Value> attributes = 19;               // 扩展属性
  int64 category_id = 20;                             // 分类ID
}

message CreateResourceResponse {
  int64 id = 1;                                       // 创建的资源ID
}

// 更新资源请求
message UpdateResourceRequest {
  int64 id = 1;                                       // 资源ID
  string display_position = 2;                        // 显示位置
  string title = 3;                                   // 资源标题
  string subtitle = 4;                                // 副标题/描述
  string content = 5;                                 // 资源内容
  string cover_image_url = 6;                         // 封面图片URL
  string cover_image_name = 7;                        // 封面图片文件名
  string target_url = 8;                              // 跳转链接URL
  int32 target_type = 9;                              // 跳转类型
  string app_id = 10;                                 // 小程序AppId
  string author = 11;                                 // 作者/发布人
  int32 sort_order = 12;                              // 排序值
  int32 status = 13;                                  // 状态
  google.protobuf.BoolValue is_display = 14;                               // 是否显示
  google.protobuf.BoolValue is_featured = 15;                              // 是否推荐
  google.protobuf.BoolValue is_top = 16;                                   // 是否置顶
  google.protobuf.Timestamp publish_time = 17;       // 发布时间
  google.protobuf.Timestamp expire_time = 18;        // 过期时间
  map<string, google.protobuf.Value> attributes = 19;               // 扩展属性
  int64 category_id = 20;                             // 分类ID
}

message UpdateResourceResponse {
  google.rpc.Status status = 1;
}

// 删除资源请求
message DeleteResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message DeleteResourceResponse {
  google.rpc.Status status = 1;
}

// ========== 搜索操作 ==========

// 搜索资源请求
message SearchResourcesRequest {
  string keyword = 1;                                 // 搜索关键词
  string resource_type_code = 2;                           // 资源类型编码
  int32 page_num = 3;                                 // 页码
  int32 page_size = 4;                                // 每页大小
}

// 搜索资源响应
message SearchResourcesResponse {
  repeated Resource resources = 1;                    // 搜索结果
  int32 page_num = 2;                                 // 当前页码
  int32 page_size = 3;                                // 每页大小
  int64 total = 4;                                    // 总记录数
  int32 pages = 5;                                    // 总页数
}

// ========== 批量操作 ==========

// 批量获取资源请求
message BatchGetResourcesRequest {
  repeated int64 ids = 1;                             // 资源ID列表
}

// 批量获取资源响应
message BatchGetResourcesResponse {
  repeated Resource resources = 1;                    // 资源列表
}

// 批量更新资源请求
message BatchUpdateResourcesRequest {
  repeated UpdateResourceRequest requests = 1;        // 更新请求列表
}

// 批量更新资源响应
message BatchUpdateResourcesResponse {
  google.rpc.Status status = 1;                       // 更新状态
}

// 批量删除资源请求
message BatchDeleteResourcesRequest {
  repeated int64 ids = 1;                             // 资源ID列表
}

message BatchDeleteResourcesResponse {
  google.rpc.Status status = 1;
}

// ========== 自定义业务操作 ==========

// 发布资源请求
message PublishResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message PublishResourceResponse {
  google.rpc.Status status = 1;
}

// 取消发布资源请求
message UnpublishResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message UnpublishResourceResponse {
  google.rpc.Status status = 1;
}

// 推荐资源请求
message FeatureResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message FeatureResourceResponse {
  google.rpc.Status status = 1;
}

// 取消推荐资源请求
message UnfeatureResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message UnfeatureResourceResponse {
  google.rpc.Status status = 1;
}

// 置顶资源请求
message PinResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message PinResourceResponse {
  google.rpc.Status status = 1;
}

// 取消置顶资源请求
message UnpinResourceRequest {
  int64 id = 1;                                       // 资源ID
}

message UnpinResourceResponse {
  google.rpc.Status status = 1;
}

// 增加浏览次数请求
message IncreaseViewCountRequest {
  int64 id = 1;                                       // 资源ID
  int32 increment = 2;                                // 增加的次数，默认为1
}

// ========== 分类相关消息 ==========

// 资源分类信息
message ResourceCategory {
  int64 id = 1;                                       // 分类ID
  int64 resource_type_id = 2;                         // 资源类型ID
  string resource_type_code = 3;                      // 资源类型编码
  string resource_type_name = 4;                      // 资源类型名称
  int64 parent_id = 5;                                // 父分类ID，0表示顶级分类
  string category_name = 6;                           // 分类名称
  string category_code = 7;                           // 分类编码
  string description = 8;                             // 分类描述
  int32 sort_order = 9;                               // 排序值
  bool is_enabled = 10;                               // 是否启用
  google.protobuf.Timestamp create_time = 11;        // 创建时间
  google.protobuf.Timestamp update_time = 12;        // 更新时间
  repeated ResourceCategory children = 13;            // 子分类列表
  int32 resource_count = 14;                          // 该分类下的资源数量
}

// 列出分类请求
message ListCategoriesRequest {
  string resource_type_code = 1;                      // 资源类型编码
  string parent_category_code = 2;                    // 父分类编码，空表示获取顶级分类
  bool is_enabled = 3;                                // 是否只获取启用的分类
}

// 列出分类响应
message ListCategoriesResponse {
  repeated ResourceCategory categories = 1;           // 分类列表
}

// 获取分类请求
message GetCategoryRequest {
  int64 id = 1;                                       // 分类ID
}

// 获取分类响应
message GetCategoryResponse {
  ResourceCategory category = 1;                      // 分类信息
}

// 获取分类树请求
message GetCategoryTreeRequest {
  string resource_type_code = 1;                      // 资源类型编码
  bool is_enabled = 2;                                // 是否只获取启用的分类
}

// 获取分类树响应
message GetCategoryTreeResponse {
  repeated ResourceCategory categories = 1;           // 分类树
}

// 根据分类获取资源请求
message ListResourcesByCategoryRequest {
  int64 category_id = 1;                              // 分类ID
  bool include_children = 2;                          // 是否包含子分类
  string display_position = 3;                        // 显示位置（可选）
  int32 page_num = 4;                                 // 页码，从1开始
  int32 page_size = 5;                                // 每页大小
  string sort_field = 6;                              // 排序字段
  string sort_type = 7;                               // 排序类型：asc/desc
}

// 根据分类获取资源响应
message ListResourcesByCategoryResponse {
  repeated Resource resources = 1;                    // 资源列表
  int32 page_num = 2;                                 // 当前页码
  int32 page_size = 3;                                // 每页大小
  int64 total = 4;                                    // 总记录数
  int32 pages = 5;                                    // 总页数
}

// 获取分类下推荐资源请求
message ListFeaturedResourcesByCategoryRequest {
  int64 category_id = 1;                              // 分类ID
  string display_position = 2;                        // 显示位置（可选）
  int32 page_num = 3;                                 // 页码，从1开始
  int32 page_size = 4;                                // 每页大小
  string sort_field = 5;                              // 排序字段
  string sort_type = 6;                               // 排序类型：asc/desc
}

// 获取分类下推荐资源响应
message ListFeaturedResourcesByCategoryResponse {
  repeated Resource resources = 1;                    // 推荐资源列表
  int32 page_num = 2;                                 // 当前页码
  int32 page_size = 3;                                // 每页大小
  int64 total = 4;                                    // 总记录数
  int32 pages = 5;                                    // 总页数
}

// 获取分类下热门资源请求
message ListPopularResourcesByCategoryRequest {
  int64 category_id = 1;                              // 分类ID
  string display_position = 2;                        // 显示位置（可选）
  int32 page_num = 3;                                 // 页码，从1开始
  int32 page_size = 4;                                // 每页大小
  string sort_field = 5;                              // 排序字段（默认view_count）
  string sort_type = 6;                               // 排序类型：asc/desc（默认desc）
}

// 获取分类下热门资源响应
message ListPopularResourcesByCategoryResponse {
  repeated Resource resources = 1;                    // 热门资源列表
  int32 page_num = 2;                                 // 当前页码
  int32 page_size = 3;                                // 每页大小
  int64 total = 4;                                    // 总记录数
  int32 pages = 5;                                    // 总页数
}

message IncreaseViewCountResponse {
  google.rpc.Status status = 1;
}




