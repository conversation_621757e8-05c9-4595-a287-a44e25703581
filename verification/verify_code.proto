syntax = "proto3";
option java_multiple_files = true;
option java_package = "com.blsc.zt.verification.grpc";

/**
获取用户对应的产品的核销码
 */
message GetVerificationListParams{
  /**
  * 权益编码 - 产品编码
  */
  int64 product_code = 1;
  /**
  * 用户id
  */
  int64 user_id = 3;
}

/**
请求创建核销码参数
 */
message CreateVerificationParams{
  /**
   * 权益编码 - 产品编码
   */
  int64 product_code = 1;
  /**
   * 权益名称 -产品名称
   */
  string product_title = 2;
  /**
  产品二级标题
   */
  string product_sub_title=3;
  /**
   * 用户id
   */
  int64 user_id = 4;
}

/**
请求核销
 */
message RequestVerifyCodeParams{
  /**
  商户id
   */
  int64 merchant_id=1;
  /**
  权益编码
   */
  int64 product_code=2;
  /**
  核销码
   */
  string verification_code=3;
}
/**
请求核销返回值
 */
message RequestVerifyCodeResponse{
  /**
 响应值
  */
  int32  result_code = 1;
  /**
  用户id
   */
  int64  user_id=2;
}

/**
请求创建核销码返回值
 */
message CreateVerificationResponse{
  /**
  响应值
   */
  int32  result_code = 1;
  /**
  核销码
   */
  string verification = 2;

}
/**
获取用户对应的产品的核销码，有多个
 */
message GetVerificationListResponse{
  /**
  响应值
 */
  repeated string verify_code = 1;
}
/**
核销码grpc服务
 */
service  VerificationService{
  /**
  生成核销码
   */
  rpc requestCreateVerification(CreateVerificationParams) returns (CreateVerificationResponse);
  /**
  核销
   */
  rpc requestVerify(RequestVerifyCodeParams) returns(RequestVerifyCodeResponse);

  /**
   获取用户对应的产品的核销码,如果存在已使用，返回相应的数量，如果无则返回0
   */
  rpc requestGetVerificationList(GetVerificationListParams) returns(GetVerificationListResponse);
}


