syntax = "proto3";

package com.blsc.users;

import "google/protobuf/wrappers.proto";

option java_multiple_files = true;
option java_package = "com.blsc.users.grpc";

service UsersAccount {
    // 手机号->批量获取用户id
    rpc GetBatchUserIds (GetBatchUserIdsRequest) returns (GetBatchUserIdsResponse);
    //获取编或解码用户id
    rpc GetUserId (GetUserIdRequest) returns (UserIdResponse) {};
}

message GetBatchUserIdsRequest {
    //list->手机号集合
    repeated string phone_numbers = 1;
}

message GetBatchUserIdsResponse {
    // list->用户id集合
    repeated string user_id = 1;
}

// 操作类型枚举
enum OperationType {
    // 编码操作
    OPERATION_TYPE_ENCRYPT = 0;
    // 解码操作
    OPERATION_TYPE_DECRYPT = 1;
}

// 用户ID处理请求的消息体
message GetUserIdRequest {
    // 操作类型：编或解码
    OperationType operation_type = 1;

    oneof id_info {
        // 如果是编码操作，则提供未编码的用户ID
        int64 user_id = 2;

        // 如果是解码操作，则提供编码后的用户ID字符串
        string encrypted_user_id = 3;
    }
}


// 用户ID处理响应的消息体
message UserIdResponse {
    oneof result {
        // 编码后的用户ID字符串
        string encrypted_user_id = 1;

        // 解码后的用户ID，64位整数
        int64 user_id = 2;
    }
}