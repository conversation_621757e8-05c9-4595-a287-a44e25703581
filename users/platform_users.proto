syntax = "proto3";

package com.blsc.users;

import "google/type/date.proto";

import "google/rpc/status.proto";

option java_multiple_files = true;
option java_package = "com.blsc.users.grpc";

service PlatformUsersService {
    // userId和appId->获取用户全部信息
    rpc GetPlatformUser (GetPlatformUserRequest) returns (GetPlatformUserResponse) {};
    // openId -> 获取用户真实姓名
    rpc GetUserRealName (GetUserRealNameRequest) returns (GetUserRealNameResponse) {};
    // List<userId> -> 获取用户openId
    rpc GetOpenId (GetOpenIdRequest) returns (GetOpenIdResponse) {};
    //List<userId>-> 获取用户虚拟信息
    rpc GetBatchVirtualInfo (GetBatchVirtualInfoRequest) returns (GetBatchVirtualInfoResponse){};
    // 新增用户信息
    rpc AddUsersInfo (AddUsersInfoRequest) returns (AddUsersInfoResponse) {};
    //获取用户信息
    rpc GetUsersInfo (GetUsersInfoRequest) returns (GetUsersInfoResponse) {};
    //查询用户信息(明文)
    rpc QueryUsersInfo (GetUsersInfoRequest) returns (QueryUsersInfoResponse){};
}

message GetPlatformUserRequest {
    //用户id
    int64 user_id = 1;
    //系统定义的应用id,非微信小程序appid
    //小桔子：mini:xjz 万峰林：mini:wfl 年卡：mini:nk
    string app_id = 2;
}

message GetPlatformUserResponse {
    //用户id
    int64 user_id = 1;
    //用户登录手机号
    string user_login_phone = 2;
    //微信开放平台unionId,用来识别同一开放平台的唯一用户
    string wx_union_id = 3;
    //用户真实姓名
    string user_real_name = 4;
    //用户联系地址
    string user_address = 5;
    //用户生日
    google.type.Date user_birthday = 6;
    //用户车牌
    string user_license_plate = 7;
    //用户身份证
    string user_identity_card = 8;
    //用户联系电话
    string user_phone = 9;
    //用户头像
    string user_avatar = 10;
    //用户昵称
    string user_nickname = 11;
    //不同小程序openid
    string wx_open_id = 12;
    //系统定义的应用id,非微信小程序appid
    string app_id = 13;
}

message GetUserRealNameRequest {
    // 微信openId
    string open_id = 1;
}

message GetUserRealNameResponse {
    //用户真实姓名
    string user_real_name = 1;
}

message GetOpenIdRequest {
    repeated UserInfo user_info = 1;
}

message GetOpenIdResponse {
    repeated WxUserInfo wx_user_info = 1;
}

message UserInfo {
    // userId 集合
    int64 user_id = 1;
    // appId 集合
    string app_id = 2;
}

message WxUserInfo {
    // userId 集合
    int64 user_id = 1;
    // wxOpenId 集合
    string wx_open_id = 2;
}

message GetBatchVirtualInfoRequest {
    //用户id List
    repeated int64 user_id = 1;
}

message GetBatchVirtualInfoResponse {
    //虚拟信息 list
    repeated VirtualInfo virtual_infos = 1;
}

message VirtualInfo{
    // 用户id
    int64 user_id = 1;
    //用户头像
    string user_avatar = 2;
    //用户昵称
    string nickname = 3;
}

message AddUsersInfoRequest {
    //用户id
    int64 user_id = 1;
    //用户联系电话
    string user_phone = 2;
    //用户真实姓名
    string user_real_name = 3;
    //用户车牌
    string user_license_plate = 4;
    //用户身份证
    string user_identity_card = 5;
}

message AddUsersInfoResponse {
    //0为成功 1为失败
    int32 status = 1;
}

message GetUsersInfoRequest {
    //用户id
    int64 user_id = 1;
}
message GetUsersInfoResponse {
    //用户id
    int64 user_id = 1;
    //用户真实姓名
    string user_real_name = 2;
    //用户联系地址
    string user_address = 3;
    //用户生日
    google.type.Date user_birthday = 4;
    //用户车牌
    string user_license_plate = 5;
    //用户身份证
    string user_identity_card = 6;
    //用户联系电话
    string user_phone = 7;
    //请求状态值
    google.rpc.Status status = 8;
}

message QueryUsersInfoResponse {
    //用户真实姓名
    string user_real_name = 2;
    //用户身份证
    string user_identity_card = 6;
    //用户联系电话
    string user_phone = 7;
    //请求状态值
    google.rpc.Status status = 8;
}